#include "stdafx.h"
#include "SpDeleteCharacterImmediate.h"

#include "ADO/NSAdoCommand.h"

SpDeleteCharacterImmediate::SpDeleteCharacterImmediate(const int64_t cid, const int32_t deletableLevel) : Input{ cid, deletableLevel }
{
}

EErrorCode SpDeleteCharacterImmediate::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("CID", Input.Cid);
	command->SetItem("DeletableLevel", Input.DeletableLevel);

	return EErrorCode::None;
}
