#include "stdafx.h"
#include "NSWorldServerSession.h"


NSWorldServerSession::NSWorldServerSession()
{
}

NSWorldServerSession::~NSWorldServerSession()
{
}

void NSWorldServerSession::Reset()
{
	m_SocketChannel = nullptr;
}

int64_t NSWorldServerSession::GetSocketChannelId() const
{
	if (m_SocketChannel == nullptr)
		return 0;

	return m_SocketChannel->GetChannelID();
}

void NSWorldServerSession::SetChannel(const std::shared_ptr<CNLIOInterfaceAdapter>& channel)
{
	if (channel == nullptr)
	{
		assert(false);
		return;
	}

	m_SocketChannel = channel;
}

const std::shared_ptr<CNLIOInterfaceAdapter>& NSWorldServerSession::GetChannel() const
{
	return m_SocketChannel;
}

auto NSWorldServerSession::GetIP() const -> const std::string&
{
	static std::string empty = "";

	if (m_SocketChannel == nullptr)
	{
		return empty;
	}
	return m_SocketChannel->GetIpAddress();
}

void NSWorldServerSession::Close() const
{
	if (m_SocketChannel)
	{
		m_SocketChannel->Close(nullptr);
	}
}

void NSWorldServerSession::SetWorldServer(std::shared_ptr<NSWorldServer> server)
{
	m_WorldServer = server;
}

std::shared_ptr<NSWorldServer> NSWorldServerSession::GetWorldServer() const
{
	return m_WorldServer;
}