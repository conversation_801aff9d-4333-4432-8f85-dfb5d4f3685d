#pragma once

#include "NSSingleton.h"

class NSWaitReconnectManager :public TemplateSingleton<NSWaitReconnectManager>
{
public:
	NSWaitReconnectManager();
	~NSWaitReconnectManager();

	void Initialize();
	void Process(uint64_t nowTick);

	void AddWaitReconnect(int64_t aid, NSWaitReconnectContext context);
	auto GetWaitReconnect(int64_t aid)->std::optional<NSWaitReconnectContext>;

	void RemoveWaitReconnect(int64_t aid);



private:
	std::unordered_map<int64_t, NSWaitReconnectContext> m_WaitReconnectList;
};
