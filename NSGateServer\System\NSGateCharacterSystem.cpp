#include "stdafx.h"
#include "NSGateCharacterSystem.h"

#include "Network/NSClientSessionManager.h"
#include "Network/NSClientSession.h"
#include "Module/NSGateModule.h"

#include "Data/NSBannedWordTemplate.h"
#include "Data/NSGameConfigTemplate.h"
#include "Data/NSCostumeTemplate.h"
#include "Data/NSWorldTemplate.h"

#include "DataBase/StoredProcedure/NSStoredProcedureBatch.h"

#include "NPModels.h"
#include "DataBase/StoredProcedure/Gate/SpCheckCharacterName.h"
#include "DataBase/StoredProcedure/Gate/SpGetCharacterList.h"
#include "DataBase/StoredProcedure/Gate/SpGetEquipmentListForLobby.h"
#include "DataBase/StoredProcedure/Gate/SpGetCharacterCustomizeListForLobby.h"
#include "DataBase/StoredProcedure/Gate/SpDeleteCharacter.h"
#include "DataBase/StoredProcedure/Gate/SpDeleteCharacterImmediate.h"
#include "DataBase/StoredProcedure/Gate/SpCancelDeleteCharacter.h"
#include "DataBase/StoredProcedure/Gate/SpGetEquipment.h"
#include "DataBase/StoredProcedure/Gate/SpGetCharacterRegionId.h"
#include "DataBase/StoredProcedure/Gate/SpCreateCharacter.h"
#include "DataBase/StoredProcedure/Gate/SpSelectCostumesForLobby.h"
#include "DataBase/StoredProcedure/Gate/SpInsertCharacterWorld.h"
#include "DataBase/StoredProcedure/Gate/SpDeleteCharacterWorld.h"
#include "DataBase/NSDataBaseManager.h"
#include "NSTickManager/NSTickManager.h"

#include "Guid/NSGuidManager.h"

#include "Network/NSGatePacketRouter.h"

#include "Log/NSActionLog.h"
#include "WaitReconnect/NSWaitReconnectManager.h"

NSGateCharacterSystem::NSGateCharacterSystem(NSGateModule* gameModule)
	: NSGateSystemBase(gameModule)
{
}

bool NSGateCharacterSystem::Init()
{
	constexpr auto InputIntervalMS = 200;
	INSERT_PROCESSOR_INTERVAL(NSGateCharacterSystem::PacketCheckCharacterNameReq, NPPacketCheckCharacterNameReq, InputIntervalMS);
	INSERT_PROCESSOR_INTERVAL(NSGateCharacterSystem::PacketCharacterListReq, NPPacketCharacterListReq, InputIntervalMS);
	INSERT_PROCESSOR_INTERVAL(NSGateCharacterSystem::PacketCreateCharacterReq, NPPacketCreateCharacterReq, InputIntervalMS);
	INSERT_PROCESSOR_INTERVAL(NSGateCharacterSystem::PacketDeleteCharacterReq, NPPacketDeleteCharacterReq, InputIntervalMS);
	INSERT_PROCESSOR_INTERVAL(NSGateCharacterSystem::PacketDeleteCharacterImmediateReq, NPPacketDeleteCharacterImmediateReq, InputIntervalMS);
	INSERT_PROCESSOR_INTERVAL(NSGateCharacterSystem::PacketUndeleteCharacterReq, NPPacketUndeleteCharacterReq, InputIntervalMS);
	INSERT_PROCESSOR_INTERVAL(NSGateCharacterSystem::PacketSelectCharacterReq, NPPacketSelectCharacterReq, InputIntervalMS);

	return true;
}

bool NSGateCharacterSystem::Reset()
{
	return true;
}

void NSGateCharacterSystem::PacketCheckCharacterNameReq(std::shared_ptr<NSClientSession>& session, NPPacketCheckCharacterNameReq* packet)
{
	if (session == nullptr || packet == nullptr)
		return;

	EErrorCode errorCode = NSBannedWordTemplate::GetInstance()->IsValidCharacterName(packet->GetName(), CHECK_VALID_NAME_EXACT);
	if (errorCode != EErrorCode::None)
	{
		NPPacketCheckCharacterNameAck ack;
		ack.SetResult(EErrorCode::AccountNameNotAllow);
		session->Send(ack);
		return;
	}

	const int32_t wid = Service::GetInstance()->GetWid();

	LOGI << std::format("PacketCheckCharacterNameReq : socket ChannelId : {}, wid : {}", session->GetSessionId(), wid);

	NSDataSerializer dataSerializer;
	dataSerializer.Create<SpCheckCharacterName>(packet->GetName(), wid);
	NSDataBaseManager::GetInstance()->StartQuery<SpCheckCharacterName>(session, dataSerializer)
		.Then([](const std::shared_ptr<NSQueryData> queryData)
			{
				ResultCheckCharacterName(queryData);
			});
}

void NSGateCharacterSystem::PacketCharacterListReq(std::shared_ptr<NSClientSession>& session, NPPacketCharacterListReq* packet)
{
	if (session == nullptr || packet == nullptr)
		return;

	int64_t aid = session->GetAID();
	int32_t wid = Service::GetInstance()->GetWid();

	LOGI << std::format("PacketCharacterListReq : socket ChannelId : {}, aid : {}, wid : {}", session->GetSessionId(), aid, wid);

	NSDataSerializer dataSerializer;
	SetDBQueryForLobbyCharacter(aid, wid, dataSerializer);

	NSDataBaseManager::GetInstance()->StartQuery<NSStoredProcedureBatch>(session, dataSerializer)
		.Then([this](std::shared_ptr<NSQueryData> spQueryData)
			{
				ResultGetCharacterList(spQueryData);
			});
}

void NSGateCharacterSystem::PacketCreateCharacterReq(std::shared_ptr<NSClientSession>& clientSession, NPPacketCreateCharacterReq* packet)
{
	if (clientSession == nullptr || packet == nullptr)
		return;

	auto aid = clientSession->GetAID();
	auto wid = Service::GetInstance()->GetWid();

	auto classType = static_cast<ENpLib_CharacterClassType>(packet->GetClassType());
	auto gender = packet->GetGender();
	auto height = packet->GetHeight();
	std::string name = packet->GetName();

	EErrorCode errorCode = NSBannedWordTemplate::GetInstance()->IsValidCharacterName(name.c_str(), CHECK_VALID_NAME_EXACT);
	if (!clientSession->IsDummyUser() && errorCode != EErrorCode::None)
	{
		NPPacketCreateCharacterAck cAck;
		cAck.SetResult(errorCode);
		clientSession->Send(cAck);
		return;
	}

	int64_t cid = NSGuidManager::GetInstance()->GenerateGuid(NSGuidManager::Guid::Character);
	const std::vector<uint8_t>& customizeData = packet->GetClassCustomize();
	std::string customizePayload;
	{
		json11::Json::array customizeJson;
		uint32_t key = 1;
		for (auto& data : customizeData)
		{
			customizeJson.push_back(json11::Json::object{
				{ "K", key++ },
				{ "V", data },
				});
		}
		customizePayload = json11::Json(customizeJson).dump();
	}
	LOGV << std::format("Customize payload: {}", customizePayload);

	NSDataSerializer dataSerializer;
	if (!dataSerializer.Create<SpCreateCharacter>(aid, cid, wid, name.c_str(), classType, gender, height, customizePayload.c_str()))
	{
		NPPacketCreateCharacterAck cAck;
		cAck.SetResult(EErrorCode::CharacterError);
		clientSession->Send(cAck);
		return;
	}

	NSDataBaseManager::GetInstance()->StartQuery<SpCreateCharacter>(clientSession, dataSerializer)
		.Then([this](std::shared_ptr<NSQueryData> spQueryData)
			{
				ResultCreateCharacter(spQueryData);
			});
}

void NSGateCharacterSystem::PacketDeleteCharacterReq(std::shared_ptr<NSClientSession>& session, NPPacketDeleteCharacterReq* packet)
{
	if (session == nullptr || packet == nullptr)
		return;

	int64_t cid = packet->GetCID();
	int32_t deleteSec = NSGameConfigTemplate::GetInstance()->GetDeleteCharacterWaitingPeriodSec();
	if (deleteSec == 0)
	{
		deleteSec = 5;
	}

	LOGI << std::format("PacketDeleteCharacterReq : socket ChannelId : {}, cid : {}", session->GetSessionId(), cid);

	NSDataSerializer dataSerializer;
	dataSerializer.Create<SpDeleteCharacter>(cid, deleteSec);
	NSDataBaseManager::GetInstance()->StartQuery<SpDeleteCharacter>(session, dataSerializer)
		.Then([](const std::shared_ptr<NSQueryData> queryData)
			{
				ResultDeleteCharacter(queryData);
			});
}

void NSGateCharacterSystem::PacketDeleteCharacterImmediateReq(std::shared_ptr<NSClientSession>& session, NPPacketDeleteCharacterImmediateReq* packet)
{
	if (session == nullptr || packet == nullptr)
		return;

	int64_t cid = packet->GetCID();

	int32_t deletableLevel = NSGameConfigTemplate::GetInstance()->GetImmediatelyDeleteCharacterMaxLevel();
	if (deletableLevel == 0)
	{
		deletableLevel = 5;
	}

	LOGI << std::format("PacketDeleteCharacterImmediateReq : socket ChannelId : {}, cid : {}", session->GetSessionId(), cid);

	NSDataSerializer dataSerializer;
	dataSerializer.Create<SpDeleteCharacterImmediate>(cid, deletableLevel);
	NSDataBaseManager::GetInstance()->StartQuery<SpDeleteCharacterImmediate>(session, dataSerializer)
		.Then([](const std::shared_ptr<NSQueryData> queryData)
			{
				ResultDeleteImmediateCharacter(queryData);
			});
}

void NSGateCharacterSystem::PacketUndeleteCharacterReq(std::shared_ptr<NSClientSession>& session, NPPacketUndeleteCharacterReq* packet)
{
	if (session == nullptr || packet == nullptr)
		return;

	int64_t cid = packet->GetCID();

	LOGI << std::format("PacketUndeleteCharacterReq : socket ChannelId : {}, cid : {}", session->GetSessionId(), cid);

	NSDataSerializer dataSerializer;
	dataSerializer.Create<SpCancelDeleteCharacter>(cid);
	NSDataBaseManager::GetInstance()->StartQuery<SpCancelDeleteCharacter>(session, dataSerializer)
		.Then([](const std::shared_ptr<NSQueryData> queryData)
			{
				ResultUndeleteCharacter(queryData);
			});
}

void NSGateCharacterSystem::PacketSelectCharacterReq(std::shared_ptr<NSClientSession>& session, NPPacketSelectCharacterReq* packet)
{
	if (session == nullptr || packet == nullptr)
		return;

	const int64_t aid = session->GetAID();
	std::optional<NSWaitReconnectContext> context = NSWaitReconnectManager::GetInstance()->GetWaitReconnect(aid);
	if (context.has_value())
	{
		const int64_t cid = packet->GetCID();
		if (cid != context.value().Cid)
		{
			NPPacketSelectCharacterAck ack;
			ack.SetResult(EErrorCode::CharacterWaitReconnectError);
			ack.SetWaitReconnectCID(context.value().Cid);
			ack.SetWaitReconnectExpireTime(context.value().ExpireTick);
			session->Send(ack);
			return;
		}
	}

	NSDataSerializer dataSerializer;
	dataSerializer.Create<SpGetCharacterRegionId>(session->GetAID(), packet->GetCID());
	NSDataBaseManager::GetInstance()->StartQuery<SpGetCharacterRegionId>(session, dataSerializer)
		.Then([](const std::shared_ptr<NSQueryData> queryData)
			{
				ResultGetCharacterRegionId(queryData);
			});
}

void NSGateCharacterSystem::SetDBQueryForLobbyCharacter(const int64_t aid, const int32_t wid, NSDataSerializer& dataSerializer)
{
	auto batch = dataSerializer.Create<NSStoredProcedureBatch>();

	int64_t nowTick = static_cast<int64_t>(NSTickManager::GetInstance()->GetUnixTimeStamp());

	batch->AddStoredProcedure(dataSerializer.Create<SpGetCharacterList>(aid, wid, nowTick));
	batch->AddStoredProcedure(dataSerializer.Create<SpGetEquipmentListForLobby>(aid, nowTick));
	batch->AddStoredProcedure(dataSerializer.Create<SpGetCharacterCustomizeListForLobby>(aid, nowTick));
	batch->AddStoredProcedure(dataSerializer.Create<SpSelectCostumesForLobby>(aid));
}

void NSGateCharacterSystem::ResultCheckCharacterName(const std::shared_ptr<NSQueryData> queryData)
{
	auto session = queryData->GetSession<NSClientSession>();
	assert(session != nullptr);

	NPPacketCheckCharacterNameAck packet;
	if (!queryData->IsValid())
	{
		packet.SetResult(queryData->GetErrorCode());
		session->Send(packet);
		return;
	}

	const SpCheckCharacterName* spCheckCharacterName = queryData->GetQueryData().Front<SpCheckCharacterName>();
	if (spCheckCharacterName->Output.Result != 0)
	{
		packet.SetResult(EErrorCode::CharacterCreateNameDuplicate);
		session->Send(packet);
		return;
	}

	packet.SetName(spCheckCharacterName->Input.Name, sizeof(spCheckCharacterName->Input.Name));
	session->Send(packet);
}

void NSGateCharacterSystem::ResultGetCharacterList(const std::shared_ptr<NSQueryData> queryData)
{
	auto session = queryData->GetSession<NSClientSession>();
	assert(session != nullptr);

	NSAdoRecordset* characterRecordSet = queryData->GetAdoRecordSet(SpGetCharacterList::GetName());
	if (characterRecordSet == nullptr)
	{
		LOGD << std::format("Failed get recordSet. (SpGetCharacterList)");
		return;
	}

	NSAdoRecordset* equipRecordSet = queryData->GetAdoRecordSet(SpGetEquipmentListForLobby::GetName());
	if (equipRecordSet == nullptr)
	{
		LOGD << std::format("Failed get recordSet. (SpGetEquipmentListForLobby)");
		return;
	}

	NSAdoRecordset* customizeRecordSet = queryData->GetAdoRecordSet(SpGetCharacterCustomizeListForLobby::GetName());
	if (customizeRecordSet == nullptr)
	{
		LOGD << std::format("Failed get recordSet. (SpGetCharacterCustomizeListForLobby)");
		return;
	}

	NSAdoRecordset* costumeRecordSet = queryData->GetAdoRecordSet(SpSelectCostumesForLobby::GetName());
	if (costumeRecordSet == nullptr)
	{
		LOGD << std::format("Failed get recordSet. (SpSelectCostumesForLobby)");
		return;
	}

	std::map<int64_t, NPCharacterListInfo> characterInfos;

	while (!characterRecordSet->IsEOF())
	{
		NPCharacterListInfo info;

		characterRecordSet->GetItem("CID", info.CID);
		characterRecordSet->GetItem("Name", info.Name);
		characterRecordSet->GetItem("CharacterDataID", info.CharacterDataID);
		characterRecordSet->GetItem("Level", info.Level);
		characterRecordSet->GetItem("RegionID", info.RegionID);
		characterRecordSet->GetItem("MainWeaponType", info.MainWeaponType);
		characterRecordSet->GetItem("UpdateAt", info.UpdateAt);
		characterRecordSet->GetItem("DeleteReqAt", info.DeleteAt);
		characterRecordSet->GetItem("Gender", info.Gender);
		characterRecordSet->GetItem("Height", info.Height);
		characterRecordSet->GetItem("AwakenClassType", info.AwakenClassType);
		characterRecordSet->GetItem("ShowHelmet", info.ShowHelmet);

		characterInfos.emplace(info.CID, info);
	}

	int32_t equipArrIdx = 0;
	int64_t preCid = 0;

	while (!equipRecordSet->IsEOF())
	{
		int64_t cid = 0;
		NPEquipment equipment;

		equipRecordSet->GetItem("CID", cid);

		if (cid != preCid)
		{
			equipArrIdx = 0;
		}

		// NOTE. SpGetCharacterList, SpGetEquipmentListForLobby, 두 sp안에서 호출되는 GETDATE의 찰나의 차이때문에
		// 삭제된 캐릭터의 아이템을 읽어올수 있어서, 조건문 추가.....ㅠ
		// 추후 개선...
		if (!characterInfos.contains(cid) || g_iMaxEquipment <= equipArrIdx)
			continue;

		equipRecordSet->GetItem("Slot", equipment.slot);
		equipRecordSet->GetItem("ItemUid", equipment.itemUid);
		equipRecordSet->GetItem("ItemId", equipment.itemId);

		characterInfos[cid].Equips[equipArrIdx++] = equipment;

		preCid = cid;
	}

	int32_t costomArrIdx = 0;
	preCid = 0;

	while (!customizeRecordSet->IsEOF())
	{
		int64_t cid = 0;
		uint8_t value = 0;

		customizeRecordSet->GetItem("CID", cid);

		if (cid != preCid)
		{
			costomArrIdx = 0;
		}

		if (!characterInfos.contains(cid) || g_uMaxCustomizeCount <= costomArrIdx)
			continue;

		customizeRecordSet->GetItem("Value", value);

		characterInfos[cid].Customize[costomArrIdx++] = value;

		preCid = cid;
	}

	while (!costumeRecordSet->IsEOF())
	{
		int64_t CID = 0;
		int32_t costumeID = 0;

		costumeRecordSet->GetItem("CID", CID);
		costumeRecordSet->GetItem("CostumeID", costumeID);

		if (characterInfos.contains(CID))
		{
			NPCharacterListInfo& info = characterInfos[CID];

			NPCostume costume = { .DataID = costumeID };

			const Costume* costumeDesignData = NSCostumeTemplate::GetInstance()->GetCostume(costumeID);
			if (costumeDesignData != nullptr)
			{
				uint8_t slotIndex = static_cast<uint8_t>(costumeDesignData->CostumeEquipType);

				info.Costumes.Costumes[slotIndex] = costume;
			}
		}
	}

	LOGI << std::format("Send NPPacketCharacterListAck : socket ChannelId : {}, aid : {}", session->GetSessionId(), session->GetAID());

	std::optional<NSWaitReconnectContext> context = NSWaitReconnectManager::GetInstance()->GetWaitReconnect(session->GetAID());

	NPPacketCharacterListAck packet;
	for (auto& info : characterInfos | std::views::values)
	{
		if (context.has_value())
		{
			if (context.value().Cid == info.CID)
			{
				info.RegionID = context.value().RegionId;
			}
		}

		packet.AddCharacterList(info);
	}


	session->Send(packet);
}

void NSGateCharacterSystem::ResultCreateCharacter(const std::shared_ptr<NSQueryData> queryData)
{
	auto session = queryData->GetSession<NSClientSession>();
	assert(session != nullptr);
	if (session == nullptr)
		return;

	if (!queryData->IsValid())
	{
		NPPacketCreateCharacterAck Ack;
		Ack.SetResult(queryData->GetErrorCode());
		session->Send(Ack);
		return;
	}

	auto spCreateCharacter = queryData->GetQueryData().Front<SpCreateCharacter>();
	if (spCreateCharacter == nullptr)
	{
		NPPacketCreateCharacterAck Ack;
		Ack.SetResult(EErrorCode::CharacterError);
		session->Send(Ack);
		return;
	}

	NPCharacterListInfo info;

	auto recordSet = queryData->GetAdoRecordSet();
	while (!recordSet->IsEOF())
	{
		recordSet->GetItem("CID", info.CID);
		recordSet->GetItem("Name", info.Name);
		recordSet->GetItem("CharacterDataID", info.CharacterDataID);
		recordSet->GetItem("Level", info.Level);
		recordSet->GetItem("RegionID", info.RegionID);
		recordSet->GetItem("UpdateAt", info.UpdateAt);
		recordSet->GetItem("Gender", info.Gender);
		recordSet->GetItem("Height", info.Height);
		recordSet->GetItem("AwakenClassType", info.AwakenClassType);		
	}

	NSActionLog::CreateCharacter(session, info.Name, info.CID, spCreateCharacter->Input.ClassType);
	NSActionLog::ChangeCharacterCustomize(session, info.Name, info.CID, spCreateCharacter->Input.ClassType, spCreateCharacter->Input.CustomizePayload, info.Gender);

	NPPacketCreateCharacterAck packet;
	packet.SetCharacter(info);
	session->Send(packet);

	NSDataSerializer dataSerializer;
	dataSerializer.Create<SpInsertCharacterWorld>(Service::GetInstance()->GetWid(), session->GetPlatformID());
	NSDataBaseManager::GetInstance()->StartQuery<SpInsertCharacterWorld>(dataSerializer);
}

void NSGateCharacterSystem::ResultDeleteCharacter(const std::shared_ptr<NSQueryData> queryData)
{
	auto session = queryData->GetSession<NSClientSession>();
	assert(session != nullptr);

	if (!queryData->IsValid())
	{
		session->SendCommonNak(queryData->GetErrorCode());
		return;
	}

	auto spDeleteCharacter = queryData->GetQueryData().Front<SpDeleteCharacter>();
	if (spDeleteCharacter == nullptr)
	{
		session->SendCommonNak(EErrorCode::CharacterError);
		return;
	}

	auto recordSet = queryData->GetAdoRecordSet();
	if (recordSet == nullptr)
	{
		session->SendCommonNak(EErrorCode::CharacterError);
		return;
	}

	if (recordSet->IsEOF())
	{
		LOGD << std::format("spDeleteCharacter record set is empty");
		return;
	}

	uint64_t deleteStamptime = 0;
	recordSet->GetItem("DeleteReqAt", deleteStamptime);

	int32_t remainCharacterCT = 0;
	recordSet->GetItem("RecordCount", remainCharacterCT);
	if (remainCharacterCT <= 1)
	{
		int32_t deleteSec = NSGameConfigTemplate::GetInstance()->GetDeleteCharacterWaitingPeriodSec();
		NSDataSerializer dataSerializer;
		dataSerializer.Create<SpDeleteCharacterWorld>(Service::GetInstance()->GetWid(), session->GetPlatformID(), deleteSec);
		NSDataBaseManager::GetInstance()->StartQuery<SpDeleteCharacterWorld>(dataSerializer);
	}

	NPPacketDeleteCharacterAck packet;
	packet.SetCID(spDeleteCharacter->Input.Cid);
	packet.SetTimestamp64(deleteStamptime);

	session->Send(packet);

	NSActionLog::DeleteCharacter(session, spDeleteCharacter->Input.Cid, ENpLib_DimensionCharacterDelete::Requested);
}

void NSGateCharacterSystem::ResultDeleteImmediateCharacter(const std::shared_ptr<NSQueryData> queryData)
{
	auto session = queryData->GetSession<NSClientSession>();
	assert(session != nullptr);

	if (!queryData->IsValid())
	{
		session->SendCommonNak(queryData->GetErrorCode());
		return;
	}

	auto spDeleteCharacterImmediate = queryData->GetQueryData().Front<SpDeleteCharacterImmediate>();
	if (spDeleteCharacterImmediate == nullptr)
	{
		session->SendCommonNak(EErrorCode::CharacterError);
		return;
	}

	auto recordSet = queryData->GetAdoRecordSet();
	if (recordSet == nullptr)
	{
		session->SendCommonNak(EErrorCode::CharacterError);
		return;
	}

	if (recordSet->IsEOF())
	{
		LOGD << std::format("spDeleteCharacter record set is empty");
		return;
	}

	int32_t remainCharacterCT = 0;
	uint64_t deleteStamptime = 0;

	recordSet->GetItem("DeleteReqAt", deleteStamptime);
	recordSet->GetItem("RecordCount", remainCharacterCT);
	if (remainCharacterCT <= 1)
	{
		NSDataSerializer dataSerializer;
		dataSerializer.Create<SpDeleteCharacterWorld>(Service::GetInstance()->GetWid(), session->GetPlatformID(), 1);
		NSDataBaseManager::GetInstance()->StartQuery<SpDeleteCharacterWorld>(dataSerializer);
	}

	NPPacketDeleteCharacterImmediateAck packet;
	packet.SetCID(spDeleteCharacterImmediate->Input.Cid);

	session->Send(packet);

	NSActionLog::DeleteCharacter(session, spDeleteCharacterImmediate->Input.Cid, ENpLib_DimensionCharacterDelete::DeletedImmediately);
}

void NSGateCharacterSystem::ResultUndeleteCharacter(const std::shared_ptr<NSQueryData> queryData)
{
	auto session = queryData->GetSession<NSClientSession>();
	assert(session != nullptr);

	if (!queryData->IsValid())
	{
		session->SendCommonNak(queryData->GetErrorCode());
		return;
	}

	auto spCancelDeleteCharacter = queryData->GetQueryData().Front<SpCancelDeleteCharacter>();
	if (spCancelDeleteCharacter == nullptr)
	{
		session->SendCommonNak(EErrorCode::CharacterError);
		return;
	}

	NSDataSerializer dataSerializer;
	dataSerializer.Create<SpInsertCharacterWorld>(Service::GetInstance()->GetWid(), session->GetPlatformID());
	NSDataBaseManager::GetInstance()->StartQuery<SpInsertCharacterWorld>(dataSerializer);

	NPPacketUndeleteCharacterAck packet;
	packet.SetCID(spCancelDeleteCharacter->Input.Cid);
	session->Send(packet);

}

void NSGateCharacterSystem::ResultGetCharacterRegionId(const std::shared_ptr<NSQueryData> queryData)
{
	std::shared_ptr<NSClientSession> session = queryData->GetSession<NSClientSession>();
	assert(session != nullptr);

	const int64_t sessionID = session->GetSessionId();

	if (NSClientSessionManager::GetInstance()->GetSessionBySessionId(sessionID) == nullptr)
	{
		LOGW << std::format("Connection to the session was lost while ResultGetCharacterRegionID(), sessionID[{}]", sessionID);
		return;
	}

	if (!queryData->IsValid())
	{
		NPPacketSelectCharacterAck packet;
		packet.SetResult(queryData->GetErrorCode());
		session->Send(packet);
		return;
	}

	const SpGetCharacterRegionId* spGetCharacterRegionId = queryData->GetQueryData().Front<SpGetCharacterRegionId>();
	if (spGetCharacterRegionId->Output.RegionId == 0)
	{
		NPPacketSelectCharacterAck packet;
		packet.SetResult(EErrorCode::CharacterError);
		session->Send(packet);
		return;
	}

	int32_t regionId = spGetCharacterRegionId->Output.RegionId;

	int64_t reconnectWorldServerSid = 0;
	const int64_t aid = session->GetAID();
	std::optional<NSWaitReconnectContext> context = NSWaitReconnectManager::GetInstance()->GetWaitReconnect(aid);
	if (context.has_value())
	{
		reconnectWorldServerSid = context.value().Sid;
		regionId = context.value().RegionId;
	}

	std::shared_ptr<NSWorldServer> pcServer = NSWorldServerManager::GetInstance()->GetWorldServer(reconnectWorldServerSid);
	if (pcServer == nullptr)
	{
		auto pcRegion = NSWorldTemplate::GetInstance()->FindRegion(regionId);
		if (pcRegion == nullptr)
		{
			NPPacketSelectCharacterAck packet;
			packet.SetResult(EErrorCode::RegionMapNotFound);
			session->Send(packet);
			return;
		}

		pcServer = NSWorldServerManager::GetInstance()->FindWorldServerByType(pcRegion->RegionType);
	}

	if (pcServer == nullptr)
	{
		NPPacketSelectCharacterAck packet;
		packet.SetResult(EErrorCode::InvalidWorld);
		session->Send(packet);
		return;
	}

	session->SetWorldServer(pcServer);

	NSPacketWorldLoginReq worldLoginReq;
	worldLoginReq.SetAID(spGetCharacterRegionId->Input.Aid);
	worldLoginReq.SetCID(spGetCharacterRegionId->Input.Cid);
	worldLoginReq.SetRegionID(regionId);
	worldLoginReq.SetSessionId(session->GetSessionId());
	worldLoginReq.SetIP(session->GetIpAddress().c_str());
	worldLoginReq.SetDummyInfo(NPDummyClientInfo(session->IsDummyUser()));
	worldLoginReq.SetPlatformAID(session->GetPlatformAID().c_str());
	worldLoginReq.SetCountry(session->GetCountry().c_str());
	worldLoginReq.SetOS(session->GetOS().c_str());
	worldLoginReq.SetOSVersion(session->GetOSVersion().c_str());
	worldLoginReq.SetMarket(session->GetMarket().c_str());
	worldLoginReq.SetDeviceInfo(session->GetDeviceInfo().c_str());
	worldLoginReq.SetUILanguage(session->GetUILanguage().c_str());
	worldLoginReq.SetPlatformType(session->GetPlatformType());
	worldLoginReq.SetPlatformID(session->GetPlatformID().c_str());
	worldLoginReq.SetIdentityProviderType(session->GetIdentityProviderType());
	worldLoginReq.SetIdentityProviderID(session->GetIdentityProviderID().c_str());
	session->WorldServerSend(&worldLoginReq);

	NSGatePacketRouter::GetInstance()->MoveSession(session, EGateModule::MoveServer);
}
