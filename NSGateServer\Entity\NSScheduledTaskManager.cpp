#include "stdafx.h"
#include "NSScheduledTaskManager.h"
#include "Main/NSGateLogicThread.h"

NSScheduledTaskManager::NSScheduledTaskManager()
{
}

NSScheduledTaskManager::~NSScheduledTaskManager()
{
}

void NSScheduledTaskManager::InsertScheduledTask(uint64_t delay, std::function<void()> function)
{
	InsertScheduledTask_Core(delay, true, function);
}

void NSScheduledTaskManager::InsertScheduledTaskWithoutInstantRun(uint64_t delay, std::function<void()> function)
{
	InsertScheduledTask_Core(delay, false, function);
}

void NSScheduledTaskManager::RunScheduledTask(uint64_t nowTick, bool forceRun)
{
	if (m_scheduledTaskList.empty())
		return;

	std::vector<NSGateTask*> tasks;
	for (auto itr = m_scheduledTaskList.begin(); itr != m_scheduledTaskList.end(); itr = m_scheduledTaskList.erase(itr))
	{
		auto task = *itr;

		// 50ms 내로 실행될 태스크
		if (nowTick + 50 < task->GetRunTime() && false == forceRun)
		{
			break;
		}

		tasks.push_back(task);
	}

	for (auto task : tasks)
	{
		task->Run();
		delete task;
	}
}

void NSScheduledTaskManager::ClearScheduledTask()
{
	for (auto task : m_scheduledTaskList)
	{
		delete task;
	}
	m_scheduledTaskList.clear();
}

void NSScheduledTaskManager::InsertScheduledTask_Core(uint64_t delay, bool allowInstantRun, std::function<void()> function)
{
	if (true == allowInstantRun)
	{
		if (delay <= 0)
		{
			function();
			return;
		}
	}

	NSGateTask* pTask = new NSGateTask();
	if (pTask == nullptr)
	{
		return;
	}

	uint64_t nowTick = NSGateLogicThread::GetInstance()->GetCurrentTick();
	pTask->SetTask(nowTick + delay, function);
	m_scheduledTaskList.insert(pTask);
}
