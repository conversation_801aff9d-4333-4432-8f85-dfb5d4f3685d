#pragma once

#include "Module/NSGateModule.h"

class NSGateCharacterModule : public NSGateModule
{
public:
	NSGateCharacterModule();
	~NSGateCharacterModule() override = default;

	void Init() override;

	bool OnAddSession(std::shared_ptr<NSClientSession> session) override;
	bool OnMoveSession(std::shared_ptr<NSClientSession> session) override;
	bool OnRemoveSession(std::shared_ptr<NSClientSession> session) override;
	bool OnDisconnect(std::shared_ptr<NSClientSession> session) override;

	void HandlePacket(NSPacketData* packetData);

};
