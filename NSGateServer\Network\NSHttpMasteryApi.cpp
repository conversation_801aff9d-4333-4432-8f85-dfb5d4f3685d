#include "stdafx.h"
#include "NSHttpMasteryApi.h"
#include "NSClientSessionManager.h"

void NSHttpMasteryApi::Execute()
{
	m_ExcuteReturn = OnExecute();
	Complete();
}

bool NSHttpMasteryApi::OnExecute()
{
	//check is valid -> Make same as GameserverLogic
	int64_t accountID = GetValue("aid", static_cast<int64_t>(0));

	const std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionByAID(accountID);
	if (clientSession == nullptr)
	{
		m_Result["result"] = "fail";
		m_Result["reason"] = "not found";
		return false;
	}

	m_Result["result"] = "success";

	return true;
}

bool NSHttpMasteryApi::IsValidWeaponMastery()
{
	return true;
}