#include "stdafx.h"

#include "NSWaitReconnectManager.h"
#include "Main/NSGateLogicThread.h"
#include "Network/NSCommunityServerHandler.h"

NSWaitReconnectManager::NSWaitReconnectManager()
{
}

NSWaitReconnectManager::~NSWaitReconnectManager()
{
}

void NSWaitReconnectManager::Initialize()
{
}

void NSWaitReconnectManager::Process(uint64_t nowTick)
{
	const auto curTick = nowTick;
	for (auto it = m_WaitReconnectList.begin(); it != m_WaitReconnectList.end();)
	{
		const NSWaitReconnectContext& waitReconnectInfo = it->second;
		const bool remove = curTick > waitReconnectInfo.ExpireTick;
		if (remove)
		{
			auto communitySession = NSCommunityServerHandler::GetInstance()->GetSession(it->second.Aid);
			if (communitySession != nullptr)
			{
				NSPacketLogoutSyncToCommunityServerNtf ntf;
				ntf.SetAID(it->second.Aid);
				communitySession->Send(ntf);
			}

			it = m_WaitReconnectList.erase(it);
			continue;
		}

		++it;
	}
}

void NSWaitReconnectManager::AddWaitReconnect(int64_t aid, NSWaitReconnectContext context)
{
	m_WaitReconnectList.emplace(aid, context);
}

auto NSWaitReconnectManager::GetWaitReconnect(int64_t aid) -> std::optional<NSWaitReconnectContext>
{
	auto it = m_WaitReconnectList.find(aid);
	if(it != m_WaitReconnectList.end())
	{
		return it->second;
	}

	return std::nullopt;
}

void NSWaitReconnectManager::RemoveWaitReconnect(int64_t aid)
{
	m_WaitReconnectList.erase(aid);
}

