#include "stdafx.h"
#include "NSHttpUserDuplicateLoginKickApi.h"
#include "NSClientSessionManager.h"

void NSHttpDuplicateLoginKickApi::Execute()
{
	m_ExcuteReturn = OnExecute();
	Complete();
}

bool NSHttpDuplicateLoginKickApi::OnExecute()
{
	const std::string& pID = GetValue("PID", "");


	const std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionByActiveSessionId(pID);
	if (clientSession == nullptr)
	{
		m_Result["result"] = "fail";
		m_Result["reason"] = "not found";
		return false;		
	}

	clientSession->SendSystemNtfThenClose(EErrorCode::DuplicateLogin);
	const int64_t socketChannelID = clientSession->GetSocketChannelId();
	NSClientSessionManager::GetInstance()->CloseSession(socketChannelID);

	m_Result["result"] = "success";

	return true;
}
