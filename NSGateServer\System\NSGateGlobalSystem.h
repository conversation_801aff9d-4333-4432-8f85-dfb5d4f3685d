#pragma once

#include "System/SystemBase/NSGateSystemBase.h"

class NSClientSession;
class NSGateModule;
class NPPacketServerLocalTimeReq;
class NPPacketRoundTripReq;
class NPPacketCreateDummyPacketReq;
class NPPacketXignCodeDataReq;
class NSGateGlobalSystem : public NSGateSystemBase
{
public:
	explicit NSGateGlobalSystem(NSGateModule* gateModule);
	virtual ~NSGateGlobalSystem() = default;

public:
	virtual bool Init() override;
	virtual bool Reset() override;

	void PacketServerLocalTimeReq(std::shared_ptr<NSClientSession>& session, NPPacketServerLocalTimeReq* packet);
	void PacketXignCodeDataReq(std::shared_ptr<NSClientSession>& session, NPPacketXignCodeDataReq* packet);
};
