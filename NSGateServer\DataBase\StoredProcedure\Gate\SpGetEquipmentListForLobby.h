#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpGetEquipmentListForLobby : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spGetEquipmentListForLobby";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t Aid = 0;
		int64_t NowTick = 0;
	} Input;

	struct Record
	{
		int64_t Cid = 0;
		uint8_t Slot = 0;
		int64_t ItemUid = 0;
		int32_t ItemId = 0;
	};

	SpGetEquipmentListForLobby() = default;
	SpGetEquipmentListForLobby(const int64_t aid, const int64_t NowTick);
};
