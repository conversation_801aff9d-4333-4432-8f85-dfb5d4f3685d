# Database_Maria 런타임 문제점 분석 보고서

## 📋 분석 개요

**분석 일자**: 2025-07-25  
**분석 환경**: MMO 게임서버 (Windows, 멀티프로세스, 프로세스당 최대 3000명, C++, MariaDB DLL)  
**분석 범위**: 런타임에서만 발생 가능한 문제점 (빌드 일관성 차이 무시)  
**분석 방법**: 헤더파일, 소스파일, 클래스, 라이브러리 한 줄씩 철저 검증

## 🚨 Critical Issues (즉시 수정 필요)

### 1. **게임 스레드 안전성 위반** (Critical - 데이터 무결성 위험)

**위치**: `GameThreadCallback.h:19`, `NSDataBaseManager.cpp:655`

**문제점**:
```cpp
// GameThreadCallback.h:19
static void PostToGameThread(void* executor, std::function<void()> task) {
    if (s_dispatcher) {
        s_dispatcher(std::move(task));
    } else {
        // ❌ 디스패처 미설정 시 워커 스레드에서 직접 실행!
        task();
    }
}
```

**위험 시나리오**:
- **초기화 순서 실수**: `SetGameThreadDispatcher()` 호출 누락 시 모든 DB 콜백이 워커 스레드에서 실행
- **게임 로직 멀티스레드 실행**: `.Then()` 콜백이 워커 스레드에서 실행되어 게임 상태 동시 접근
- **데이터 레이스**: 싱글스레드 가정 하에 설계된 게임 객체들의 동시 수정

**실제 위험한 코드**:
```cpp
// 게임서버에서 이런 사용 시
NSDataBaseManager::GetInstance()->StartQuery<SpUpdateGuildAcceptLimit>(conn, data)
    .Then([this, playerId](auto queryData) {
        // ❌ 이 콜백이 워커 스레드에서 실행될 수 있음!
        auto player = GetPlayer(playerId);
        player->SetGold(queryData->GetInt("gold"));        // 데이터 레이스
        RankingManager::UpdatePlayerRank(playerId);        // 전역 상태 충돌
        SendToClient(playerId, "UpdateComplete");          // 네트워크 처리 오류
    });
```

### 2. **타입 불일치로 인한 메모리 오류** (High - 메모리 오류)

**위치**: `NSMySQLConnection.cpp:169, 280`

**문제점**:
```cpp
// NSMySQLConnection.h:102 - 헤더에서는 shared_ptr 반환
std::shared_ptr<MYSQL_STMT> GetCachedStatement(const std::string& query);

// NSMySQLConnection.cpp:440 - 실제 구현에서는 MYSQL_STMT* 반환
MYSQL_STMT* NSMySQLConnection::GetCachedStatement(const std::string& query) {
    // ...
    return stmt;  // ❌ shared_ptr<MYSQL_STMT>를 MYSQL_STMT*로 반환
}

// NSMySQLConnection.cpp:169 - 사용 위치에서 타입 불일치
m_currentStmt = GetCachedStatement(query);  // ❌ shared_ptr에 MYSQL_STMT* 할당
```

**위험**: 타입 불일치로 인한 컴파일 오류 또는 런타임 메모리 오류

## 🔍 High Priority Issues

### 3. **동기화 메커니즘의 데드락 위험** (High - 시스템 정지)

**위치**: `CIDQueueManager.h:43-47`, `CIDQueueManager.cpp:16`

**문제점**:
```cpp
// CIDQueueManager.h:43-47
void UpdateAccessTime() {
    std::lock_guard<std::mutex> lock(mutex);  // ❌ 이미 락 보유 중에 호출 가능
    lastAccessTime = std::chrono::steady_clock::now();
}

// CIDQueueManager.cpp:16 - 호출 위치
std::shared_lock<std::shared_mutex> readLock(m_cidQueuesMutex);
auto it = m_cidQueues.find(cid);
if (it != m_cidQueues.end()) {
    it->second->UpdateAccessTime();  // ❌ 중첩 락으로 데드락 위험
    return it->second;
}
```

**위험**: 
- **락 순서 문제**: 다른 스레드에서 다른 순서로 락 획득 시 데드락
- **중첩 락**: `UpdateAccessTime()`이 이미 락을 보유한 상태에서 호출되어 데드락

### 4. **예외 상황에서 리소스 정리 부족** (High - 메모리 누수)

**위치**: `AsyncQueryExecutor.cpp:278`, `NSMySQLConnection.cpp:122`

**문제점**:
```cpp
// AsyncQueryExecutor.cpp:278
void AsyncQueryExecutor::CleanupTask(AsyncQueryTask& task) {
    task.stmt.reset();  // ❌ shared_ptr reset만으로는 캐시에서 제거되지 않음
    task.result.reset();
    task.connection.reset();
    // ...
}

// NSMySQLConnection.cpp:122
bool NSMySQLConnection::CheckConnection() {
    if (!m_mysql)
        return false;
    if (!m_connected.load()) {
        return Reconnect();  // ❌ 재연결 실패 시 예외 처리 부족
    }
    return true;  // ❌ 실제 네트워크 연결 상태 확인 없이 true 반환
}
```

**위험**:
- **타임아웃된 작업의 불완전한 정리**: PreparedStatement가 캐시에 남아 메모리 누수
- **연결 상태 불일치**: 재연결 실패 시 플래그와 실제 상태 불일치

## ⚠️ Medium Priority Issues

### 5. **성능 및 확장성 제한** (Medium - 성능 저하)

**위치**: `AsyncQueryPoller.cpp:178-182`, `NSDataBaseManager.cpp:398`

**문제점**:
```cpp
// AsyncQueryPoller.cpp:178-182 - 폴링 오버헤드
if (m_activeQueries.empty()) {
    std::this_thread::sleep_for(std::chrono::milliseconds(50));  // 쿼리 없을 때
} else {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));  // ❌ CPU 사용량 증가
}

// NSDataBaseManager.cpp:398 - 하드코딩된 제한
workThreadCnt = std::min(totalConnections / 2, 32u);  // ❌ 확장성 제한
```

**위험**: 고부하 상황에서 성능 저하 및 확장성 제한

### 6. **메모리 관리 최적화 부족** (Medium - 메모리 증가)

**위치**: `NSMySQLConnection.h:148`, `CIDQueueManager.cpp`

**문제점**:
- PreparedStatement 캐시 크기 100개 고정 (동적 조정 불가)
- 프로시저 메타데이터 캐시 무제한 증가
- CID별 큐 무제한 증가 (정리 메커니즘 부족)

## 🛠️ 권장 수정사항 (우선순위별)

### 즉시 수정 (Critical)

**1. 게임 스레드 디스패처 강제 설정**
```cpp
// GameThreadCallback.h 수정
static void PostToGameThread(void* executor, std::function<void()> task) {
    if (s_dispatcher) {
        s_dispatcher(std::move(task));
    } else {
        // ❌ 현재: task();
        // ✅ 개선: 강제 에러
        throw std::runtime_error("CRITICAL: Game thread dispatcher not set! "
                                "Call SetGameThreadDispatcher() before using DB operations.");
    }
}

// NSDataBaseManager.cpp Start() 메서드에 검증 추가
bool NSDataBaseManager::Start(uint32_t workThreadCnt) {
    if (!m_gameThreadPost) {
        LOGE << "CRITICAL: Game thread dispatcher not set!";
        return false;  // 시작 자체를 거부
    }
    // ...
}
```

**2. 타입 불일치 수정**
```cpp
// NSMySQLConnection.cpp:440 수정
std::shared_ptr<MYSQL_STMT> NSMySQLConnection::GetCachedStatement(const std::string& query) {
    // ... 기존 로직 ...
    return stmt;  // shared_ptr 반환으로 수정
}
```

### 단기 수정 (1주일 내)

**3. 동기화 메커니즘 개선**
```cpp
// CIDQueueManager.h 수정
std::atomic<std::chrono::steady_clock::time_point> lastAccessTime;

void UpdateAccessTime() {
    // 락 없이 atomic 사용
    lastAccessTime.store(std::chrono::steady_clock::now());
}
```

**4. 예외 처리 강화**
- 재연결 실패 시 명확한 에러 처리
- 타임아웃된 작업의 완전한 리소스 정리

### 중기 수정 (1개월 내)

**5. 성능 최적화**
- 동적 폴링 간격 조정
- 캐시 크기 동적 조정
- 메모리 정리 메커니즘 추가

## 🎯 결론

Database_Maria 라이브러리는 **기술적으로 우수한 설계**를 가지고 있으나, **핵심 안전성 규칙 위반**으로 인해 **즉시 수정이 필요**합니다.

**주요 성과**:
- ✅ CID별 순서 보장 완벽 구현
- ✅ MariaDB 비동기 API 완전 활용
- ✅ 효율적인 커넥션 풀 및 대기 큐 시스템
- ✅ mimalloc 통합으로 메모리 성능 최적화

**심각한 문제점**:
- 🚨 **게임 스레드 안전성 위반** - 워커 스레드에서 게임 로직 실행 위험
- 🚨 **타입 불일치** - 메모리 오류 위험
- 🚨 **동기화 문제** - 데드락 위험

**⚠️ 경고**: 현재 상태로 프로덕션 배포 시 **게임 데이터 무결성 문제 및 크래시 위험**이 존재합니다.

**우선순위**: **게임 스레드 강제성 확보** > **타입 안전성** > **동기화 개선** > **성능 최적화**

**권장사항**: Critical 이슈 수정 후 단계적 개선을 통해 안정적인 프로덕션 배포 준비
