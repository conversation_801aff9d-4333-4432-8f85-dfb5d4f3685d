#pragma once

#include "Module/NSGateModule.h"
#include "NSSingleton.h"

class NSLinkEntity;
class NSGateMoveServerModule : public NSGateModule
{
public:
	NSGateMoveServerModule();
	virtual ~NSGateMoveServerModule();

public:
	void Init() override;
	void Reset() override;
	void Process(uint64_t nowTick, uint64_t elapsedTickCount) override;

	bool OnAddSession(std::shared_ptr<NSClientSession> session) override;
	bool OnMoveSession(std::shared_ptr<NSClientSession> session) override;
	bool OnRemoveSession(std::shared_ptr<NSClientSession> session) override;
	bool OnDisconnect(std::shared_ptr<NSClientSession> session) override;

	void HandlePacket(NSPacketData* packetData);

private:
	template<typename System>
	bool AddSystem()
	{
		return NSGateModule::AddSystem<System>(this);
	}

};
