#include "stdafx.h"
#include "SpGetEquipmentListForLobby.h"

#include "ADO/NSAdoCommand.h"

SpGetEquipmentListForLobby::SpGetEquipmentListForLobby(const int64_t aid, const int64_t NowTick) :
	Input{ aid, NowTick }
{
}

EErrorCode SpGetEquipmentListForLobby::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("AID", Input.Aid);
	//command->SetItem("@NowTick", Input.NowTick);

	return EErrorCode::None;
}
