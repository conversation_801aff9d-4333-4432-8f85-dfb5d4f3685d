#include "stdafx.h"
#include "SpGetCharacterList.h"

#include "ADO/NSAdoCommand.h"

SpGetCharacterList::SpGetCharacterList(const int64_t aid, const int32_t wid, const int64_t NowTick) : Input{ aid, wid, NowTick }
{
}

EErrorCode SpGetCharacterList::Make<PERSON><PERSON><PERSON>(NSAdoCommand* command)
{
	command->SetItem("AID", Input.Aid);
	command->SetItem("WID", Input.Wid);
	//command->SetItem("@NowTick", Input.NowTick);
	return EErrorCode::None;
}
