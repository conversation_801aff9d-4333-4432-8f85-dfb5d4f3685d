#include "stdafx.h"
#include "Module/NSGateModuleManager.h"

#include "Data\NSWorldTemplate.h"
#include "Module/NSGateLoginModule.h"
#include "Module/NSGateCharacterModule.h"
#include "Module/NSGateWorldModule.h"
#include "Module/NSGateMoveServerModule.h"
#include "Network/NSClientSession.h"
#include "NSDefineEnum.h"
#include "Main/NSGateLogicThread.h"

NSGateModuleManager::NSGateModuleManager()
{
	Init();
}

NSGateModuleManager::~NSGateModuleManager()
{
}

void NSGateModuleManager::Init()
{
	//로그인 모듈
	NSGateLoginModule* pLoginModule = new NSGateLoginModule();
	pLoginModule->Init();
	pLoginModule->SetModuleID(static_cast<int>(EGateModule::Login));
	m_GateLoginModule.Initialize(pLoginModule);

	//캐릭터 모듈
	NSGateCharacterModule* pCharacterModule = new NSGateCharacterModule();
	pCharacterModule->Init();
	pCharacterModule->SetModuleID(static_cast<int>(EGateModule::Character));
	m_GateCharacterModule.Initialize(pCharacterModule);

	NSGateWorldModule* pGateWorldModule = new NSGateWorldModule();
	pGateWorldModule->Init();
	pGateWorldModule->SetModuleID(static_cast<int32_t>(EGateModule::World));
	m_GateWorldModule.Initialize(pGateWorldModule);

	//MoveServer (서버이동) 모듈
	NSGateMoveServerModule* pMoveServerModule = new NSGateMoveServerModule();
	pMoveServerModule->Init();
	pMoveServerModule->SetModuleID(static_cast<int>(EGateModule::MoveServer));
	m_GateMoveServerModule.Initialize(pMoveServerModule);
}

auto NSGateModuleManager::GetGateLoginModule()->NSGateLoginModule*
{
	return m_GateLoginModule.pGateModule.get();
}

auto NSGateModuleManager::GetGateCharacterModule()->NSGateCharacterModule*
{
	return m_GateCharacterModule.pGateModule.get();
}

auto NSGateModuleManager::GetGateModule(EGateModule moduleType)->NSGateModule*
{
	if (EGateModule::Login == moduleType)
	{
		return m_GateLoginModule.pGateModule.get();
	}
	else if (EGateModule::Character == moduleType)
	{
		return m_GateCharacterModule.pGateModule.get();
	}
	else if (EGateModule::World == moduleType)
	{
		return m_GateWorldModule.pGateModule.get();
	}
	else if (EGateModule::MoveServer == moduleType)
	{
		return m_GateMoveServerModule.pGateModule.get();
	}

	return nullptr;
}

void NSGateModuleManager::Process()
{
	uint64_t nowTick = NSGateLogicThread::GetInstance()->GetCurrentTick();
	if (m_GateLoginModule.pGateModule != nullptr)
	{
		m_GateLoginModule.Process(nowTick);
	}

	if (m_GateCharacterModule.pGateModule != nullptr)
	{
		m_GateCharacterModule.Process(nowTick);
	}

	if (m_GateWorldModule.pGateModule != nullptr)
	{
		m_GateWorldModule.Process(nowTick);
	}
}


