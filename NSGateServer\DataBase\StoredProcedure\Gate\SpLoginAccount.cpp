#include "stdafx.h"
#include "SpLoginAccount.h"

#include "ADO/NSAdoCommand.h"

SpLoginAccount::SpLoginAccount(const int64_t aid, const int32_t wid, const std::string_view platformAccountID,
	ENPPlatformType platformType, const std::string_view platformId, ENPPlatformType identityProviderType, const std::string_view identityProviderID) :
	Input{ aid, wid }
{
	/* Event ID:ba8567f7 Jul 7, 8:44 AM 의심 포인트
	1. char[] 에 null-termanation 이 누락된 경우
	2. SetItem 에서 null 체크 없이 사용된 경우
	3. PlatformAccountID 등에 비정상 문자열이 대입된 경우 (너무 길어서 \0 이 잘린 경우)
	*/

	//! 길이 체크 후 복사
	constexpr size_t maxLen = g_uMaxPlatformIDLength - 1;

	auto SafeCopy = [](char* dest, const std::string_view& src, size_t maxLen)
		{
			const size_t len = std::min(src.size(), maxLen);
			memcpy(dest, src.data(), len);
			dest[len] = '\0';
		};

	SafeCopy(Input.PlatformAccountID, platformAccountID, maxLen);
	Input.PlatformType = platformType;

	SafeCopy(Input.PlatformID, platformId, maxLen);
	Input.IdentityProviderType = identityProviderType;

	SafeCopy(Input.IdentityProviderID, identityProviderID, maxLen);

	/*strcpy_s(Input.PlatformAccountID, sizeof(Input.PlatformAccountID), platformAccountID.data());
	Input.PlatformType = platformType;
	strcpy_s(Input.PlatformID, sizeof(Input.PlatformID), platformId.data());
	Input.IdentityProviderType = identityProviderType;
	strcpy_s(Input.IdentityProviderID, sizeof(Input.IdentityProviderID), identityProviderID.data());*/
}

EErrorCode SpLoginAccount::MakeQuery(NSAdoCommand* command)
{
	command->SetItem("AID", Input.Aid);
	command->SetItem("WID", Input.Wid);
	if (!command->SetItem("PlatformAID", Input.PlatformAccountID))
		return EErrorCode::DBArgumentError;
	command->SetItem("PlatformType", NPClassCast(Input.PlatformType));
	if (!command->SetItem("PlatformID", Input.PlatformID))
		return EErrorCode::DBArgumentError;
	command->SetItem("IdentityProviderType", NPClassCast(Input.IdentityProviderType));
	if (!command->SetItem("IdentityProviderID", Input.IdentityProviderID))
		return EErrorCode::DBArgumentError;

	return EErrorCode::None;
}
