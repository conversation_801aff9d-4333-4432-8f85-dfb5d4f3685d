#include "stdafx.h"

#include "NSLogDefine.h"
#include "Logger/NSLogger.h"

#include "Http/NSHttpListener.h"

#include "Network/NSHttpUserKickApi.h"
#include "Network/NSHttpUserDuplicateLoginKickApi.h"
#include "Network/NSHttpWorldNoticeApi.h"
#include "Network/NSHttpMaintenanceApi.h"
#include "Network/NSWorldServerHandler.h"

static bool HttpConsoleHelp(const httplib::Request&, std::string& result)
{
	nlohmann::json commandJson;
	std::string strCommand = "exit";
	std::string strUsage = "exit";

	commandJson[strCommand] = strUsage;

	nlohmann::json resJson;
	resJson["result"] = 0;
	resJson["command"] = commandJson;
	result = resJson.dump();
	return true;
}

static bool HttpConsoleCommand(const httplib::Request& req, std::string& result)
{
	if (!req.body.empty())
	{
		const nlohmann::json reqJson = nlohmann::json::parse(req.body);
		if (reqJson.contains("command"))
		{
			std::string strCommand = reqJson["command"];
			std::wstring command = NSUtil::AnsiToWide(strCommand.c_str());
			NSWorldServerHandler::GetInstance()->AddConsoleCommand(command.c_str());
		}
	}

	nlohmann::json resJson;
	resJson["result"] = 0;
	result = resJson.dump();
	return true;
}

void InitializeHttp(uint16_t port)
{
	const SystemContext& context = System::GetInstance()->GetContext();
	NSHttpListener* http = NSHttpListener::GetInstance();

	if (!context.Http.DocumentRoot.empty())
	{
		http->Mount("/", context.Http.DocumentRoot.c_str());
	}

	http->Get("/console", HttpConsoleHelp).Post("/console", HttpConsoleCommand);
	http->Registers<
		NSHttpUserKickApi,
		NSHttpDuplicateLoginKickApi,
		NSHttpWorldNoticeApi,
		NSHttpMaintenanceApi
	>();

	http->Listen(port);
}
