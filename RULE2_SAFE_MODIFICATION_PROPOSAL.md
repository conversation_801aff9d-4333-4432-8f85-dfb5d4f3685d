# 규칙 2번 게임 스레드 복귀 - 강제 실행 수정 방안

## 핵심 원칙
**모든 DB 콜백은 반드시 게임 로직 스레드에서 실행되어야 함**
- 테스트 환경도 예외 없음
- 워커 스레드 직접 실행 완전 금지
- 디스패처 미설정 시 초기화 실패

## 수정 방안: 디스패처 필수화

### GameThreadCallback.h 수정
```cpp
#pragma once
#include <functional>
#include <atomic>
#include <stdexcept>

class GameThreadCallback
{
public:
    // 게임 스레드로 작업 전달
    static void PostToGameThread(void* executor, std::function<void()> task)
    {
        if (s_dispatcher)
        {
            s_dispatcher(std::move(task));
        }
        else
        {
            // 디스패처가 없으면 무조건 예외 발생
            throw std::runtime_error(
                "CRITICAL: Game thread dispatcher not set! "
                "All DB callbacks MUST run on game thread. "
                "Call SetGameThreadDispatcher() before ANY DB operations."
            );
        }
    }
    
    // 디스패처 설정 여부 확인
    static bool IsDispatcherSet() {
        return s_dispatcher != nullptr;
    }
    
    // 게임 서버에서 디스패처 설정
    static void SetGameThreadDispatcher(std::function<void(std::function<void()>)> dispatcher)
    {
        if (!dispatcher) {
            throw std::invalid_argument("Dispatcher cannot be null");
        }
        s_dispatcher = std::move(dispatcher);
    }
    
private:
    static inline std::function<void(std::function<void()>)> s_dispatcher;
```

### NSDataBaseManager 수정
```cpp
// Initialize 메서드에 검증 추가 (Start보다 먼저)
bool NSDataBaseManager::Initialize()
{
    if (m_initialized)
        return true;
    
    // 디스패처 필수 검증
    if (!GameThreadCallback::IsDispatcherSet()) {
        LOGE << "CRITICAL: Cannot initialize Database Manager without game thread dispatcher! "
             << "Call SetGameThreadDispatcher() BEFORE Initialize().";
        return false;
    }
    
    // ... 기존 초기화 코드 ...
    m_initialized = true;
    return true;
}

// Start 메서드에도 재검증 (이중 안전장치)
bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
    if (!m_initialized)
        return false;
    
    // 디스패처 재확인
    if (!m_gameThreadPost) {
        LOGE << "CRITICAL: Game thread dispatcher not properly set! "
             << "Database Manager cannot start without dispatcher.";
        return false;
    }
    
    // ... 기존 코드 ...
}

// 쿼리 실행 콜백 처리 부분
if (gameThreadPost && instance && !instance->m_isShuttingDown.load())
{
    gameThreadPost([task]() mutable {
        task.promise.SetValue(task.queryData);
    });
}
else
{
    // 에러 처리만 수행 (절대 직접 실행하지 않음)
    LOGE << "CRITICAL: Cannot complete DB operation - game thread dispatcher unavailable";
    task.queryData->SetError("Game thread dispatcher not available");
    task.promise.SetError(std::runtime_error(
        "DB operation failed: All callbacks must run on game thread"
    ));
}
```

## 테스트 환경을 위한 필수 구성

테스트 환경에서도 게임 스레드를 시뮬레이션해야 합니다:

```cpp
// 테스트용 간단한 게임 스레드 시뮬레이터
class TestGameThread {
    std::queue<std::function<void()>> m_tasks;
    std::mutex m_mutex;
    std::condition_variable m_cv;
    std::thread m_thread;
    std::atomic<bool> m_running{true};
    
public:
    TestGameThread() {
        m_thread = std::thread([this] {
            while (m_running) {
                std::unique_lock<std::mutex> lock(m_mutex);
                m_cv.wait(lock, [this] { return !m_tasks.empty() || !m_running; });
                
                while (!m_tasks.empty()) {
                    auto task = std::move(m_tasks.front());
                    m_tasks.pop();
                    lock.unlock();
                    task();
                    lock.lock();
                }
            }
        });
    }
    
    void Post(std::function<void()> task) {
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_tasks.push(std::move(task));
        }
        m_cv.notify_one();
    }
    
    ~TestGameThread() {
        m_running = false;
        m_cv.notify_all();
        m_thread.join();
    }
};
```

## 즉시 적용 방안

### 1. 컴파일 에러로 강제
```cpp
// GameThreadCallback.h의 task() 직접 호출 부분 제거
static void PostToGameThread(void* executor, std::function<void()> task)
{
    if (!s_dispatcher) {
        // 컴파일 시점에 발견되도록 static_assert 활용
        #ifndef NDEBUG
        assert(false && "Game thread dispatcher must be set!");
        #endif
        throw std::runtime_error("Game thread dispatcher not set!");
    }
    s_dispatcher(std::move(task));
}
```

### 2. 초기화 순서 강제
```cpp
// NSDataBaseManager에 초기화 플래그 추가
class NSDataBaseManager {
    std::atomic<bool> m_dispatcherVerified{false};
    
public:
    // SetGameThreadDispatcher 호출 시 검증 플래그 설정
    void SetGameThreadDispatcher(GameThreadPostFunc dispatcher) {
        m_gameThreadPost = dispatcher;
        GameThreadCallback::SetGameThreadDispatcher(dispatcher);
        m_dispatcherVerified = true;
    }
    
    // Initialize에서 검증
    bool Initialize() {
        if (!m_dispatcherVerified) {
            LOGE << "SetGameThreadDispatcher() must be called before Initialize()";
            return false;
        }
        // ...
    }
};
```

## 사용 예시

### 게임 서버 (프로덕션)
```cpp
int main() {
    auto* dbManager = NSDataBaseManager::GetInstance();
    
    // 반드시 Initialize 전에 디스패처 설정
    dbManager->SetGameThreadDispatcher([](auto task) {
        GameThreadScheduler::Post(std::move(task));
    });
    
    if (!dbManager->Initialize()) {  // 디스패처 없으면 실패
        LOG_FATAL << "Failed to initialize DB Manager";
        return -1;
    }
    
    dbManager->Start();
}
```

### 테스트 코드 (게임 스레드 시뮬레이션 필수)
```cpp
TEST(DatabaseTest, QueryExecution) {
    // 테스트용 게임 스레드 생성
    TestGameThread testGameThread;
    
    auto* dbManager = NSDataBaseManager::GetInstance();
    
    // 테스트에서도 반드시 디스패처 설정
    dbManager->SetGameThreadDispatcher([&testGameThread](auto task) {
        testGameThread.Post(std::move(task));
    });
    
    ASSERT_TRUE(dbManager->Initialize());  // 디스패처 없으면 실패
    ASSERT_TRUE(dbManager->Start());
    
    // 테스트 실행...
}
```

## 장점
1. **완전한 안전성 보장**: 워커 스레드 실행 원천 차단
2. **규칙 100% 준수**: 테스트 환경도 게임 스레드 사용
3. **초기 단계 검증**: Initialize()에서 즉시 실패
4. **명확한 에러**: 문제 원인과 해결 방법 명시

## 영향 범위 및 대응
1. **기존 게임 서버**: 이미 SetGameThreadDispatcher 호출하므로 영향 없음
2. **테스트 코드**: 
   - TestGameThread 클래스 추가 필요
   - 각 테스트에 디스패처 설정 코드 추가
3. **신규 프로젝트**: 처음부터 올바른 구조 강제

## 주의사항
- 테스트 코드 수정 필요하지만, 이는 올바른 동작을 보장하기 위함
- 단기적 불편함이 있지만 장기적 안정성 확보
- 모든 환경에서 동일한 스레드 모델 보장