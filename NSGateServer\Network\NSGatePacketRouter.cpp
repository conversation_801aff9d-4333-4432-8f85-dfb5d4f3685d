#include "stdafx.h"

#include "NSGatePacketRouter.h"

#include "Module/NSGateModule.h"
#include "Module/NSGateModuleManager.h"
#include "Module/NSGateLoginModule.h"
#include "Network/NSClientSession.h"
#include "Packet/NSPacketData.h"
#include "NSDefineEnum.h"

NSGatePacketRouter::NSGatePacketRouter()
{
}

NSGatePacketRouter::~NSGatePacketRouter()
{
}

void NSGatePacketRouter::Init()
{

}

bool NSGatePacketRouter::AddSession(std::shared_ptr<NSClientSession>& session, NSGateModule* gameModule)
{
	if (session == nullptr || gameModule == nullptr)
	{
		LOGE << std::format("[Server] PacketRouter::AddSession failed - Session or GameModule nullptr");
		return false;
	}

	if (!m_GateModuleByChannelId.emplace(session->GetSocketChannelId(), gameModule).second)
	{
		LOGE << std::format("[Server] PacketRouter::AddSession failed - GameModule connect clientSessionId[{}] ModuleID[{}]",
			session->GetSocketChannelId(), gameModule->GetModuleID());
		return false;
	}

	return gameModule->AddSession(session);
}

bool NSGatePacketRouter::MoveSession(std::shared_ptr<NSClientSession>& session, EGateModule moduleType)
{
	if (session == nullptr)
	{
		LOGE << std::format("[Server] PacketRouter::MoveSession failed - Session nullptr");
		return false;
	}

	const auto it = m_GateModuleByChannelId.find(session->GetSocketChannelId());
	if (it == m_GateModuleByChannelId.end())
	{
		LOGE << std::format("[Server] PacketRouter::MoveSession failed - Can't find GameModule[{}]", session->GetSocketChannelId());
		return false;
	}

	NSGateModule* srcGameModule = it->second;
	if (srcGameModule != nullptr)
	{
		if (false == srcGameModule->RemoveSession(session))
		{
			LOGE << std::format("[Server] PacketRouter::MoveSession failed - Can't remove session. ModuleId:[{}] Session:[{}]", srcGameModule->GetModuleID(), session->GetSocketChannelId());
			return false;
		}
	}

	NSGateModule* destGameModule = NSGateModuleManager::GetInstance()->GetGateModule(moduleType);
	if (destGameModule != nullptr)
	{
		//! 모듈 이동시 접속정보 갱신.
		it->second = destGameModule;
		destGameModule->MoveSession(session);
	}

	return true;
}

bool NSGatePacketRouter::PushPacket(NSPacketData* packetData)
{
	if (0 == packetData->GetSize())
	{
		LOGE << std::format("[Server] PacketRouter::PushPacket failed - buf or Sesson nullptr");
		return false;
	}

	int64_t socketChannelId = packetData->GetSocketChannelID();
	const auto it = m_GateModuleByChannelId.find(socketChannelId);
	if (it == m_GateModuleByChannelId.end())
	{
		LOGE << std::format("[Server] PacketRouter::PushPacket failed - Can't find GateModule SocketChannelId[{}]", socketChannelId);
		return false;
	}

	it->second->PushPacket(packetData);
	return true;

}

bool NSGatePacketRouter::HandlePacket(NSPacketData* packetData)
{
	if (0 == packetData->GetSize())
	{
		LOGE << std::format("[Server] PacketRouter::HandlePacket failed - buf or Sesson nullptr");
		return false;
	}

	int64_t socketChannelId = packetData->GetSocketChannelID();
	const auto it = m_GateModuleByChannelId.find(socketChannelId);
	if (it == m_GateModuleByChannelId.end())
	{
		LOGE << std::format("[Server] PacketRouter::HandlePacket failed - Can't find GameModule[{}]", socketChannelId);
		return false;
	}

	it->second->HandlePacket(packetData);
	return true;
}

bool NSGatePacketRouter::MoveLoginModule(std::shared_ptr<NSClientSession>& session)
{
	if (session == nullptr)
	{
		LOGE << std::format("Session is null");
		return false;
	}

	NSGateModule* loginModule = NSGateModuleManager::GetInstance()->GetGateLoginModule();
	if (loginModule == nullptr)
	{
		LOGE << std::format("LoginModule is null");
		return false;
	}

	return AddSession(session, loginModule);
}

void NSGatePacketRouter::CloseSession(const int64_t channelId)
{
	const auto it = m_GateModuleByChannelId.find(channelId);
	if (it != m_GateModuleByChannelId.end())
	{
		NSGateModule* gameModule = it->second;
		if (gameModule != nullptr)
		{
			gameModule->Disconnect(channelId);
		}

		m_GateModuleByChannelId.erase(it);
	}
}
