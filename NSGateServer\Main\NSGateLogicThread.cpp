#include "stdafx.h"
#include "NSGateLogicThread.h"
#include "NSTickManager/NSTickManager.h"
#include "Http/NSHttpListener.h"
#include "WaitReconnect/NSWaitReconnectManager.h"
#include "GMTool/NSGMToolManager.h"
#include "Network/LocalAgent/NSGateServerLocalAgentConnection.h"
#include "Network/NSCommunityServerHandler.h"
#include "Log/NSPerformanceLog.h"

NSGateLogicThread::NSGateLogicThread()
{
}

void NSGateLogicThread::Process()
{
	std::thread::id tid = std::this_thread::get_id();
	Promise::SetCurrentExecutor(tid, this);
	m_ThreadId = tid;

	while (m_Running)
	{
		uint64_t currentTick = (uint64_t)NSTickManager::GetInstance()->GetTickMS();
		uint64_t fromLastTick = currentTick - m_CurrentTick;
		m_CurrentTick = currentTick;

		CNLGlobalPipe<NSGateServerLocalAgentConnection>::Update((unsigned int)fromLastTick);

		ProcessCommunityServerTryConnect();

		NSWaitReconnectManager::GetInstance()->Process(m_CurrentTick);

		NSGMToolManager::GetInstance()->Process();

		for (const auto& handler : m_ProcessHandlerList)
		{
			handler->Process();
			CNLManualFlushRegister::GetInstance()->EndPhase();
		}

		for (const auto& handler : m_ProcessHandlerList)
		{
			handler->PostProcess();
		}

		ProcessExecutor();

		NSHttpListener::GetInstance()->Process();

		NSPerformanceLog::Log();
	}

	while (NSCommunityServerHandler::GetInstance()->IsTryConnect())
	{
		ProcessExecutor();
	}

	Promise::SetCurrentExecutor(tid, nullptr);
}

void NSGateLogicThread::ProcessCommunityServerTryConnect()
{
	if (NSCommunityServerHandler::GetInstance()->IsConnected()
		|| NSCommunityServerHandler::GetInstance()->IsTryConnect())
		return;

	if (!NSCommunityServerHandler::GetInstance()->TryFire(NSCommunityServerHandler::EState::TryConnect))
		return;

	// CommunityServerConnect
	{
		Promise::Post(Promise::ThreadPool(),
			[]()
			{
				return NSCommunityServerHandler::GetInstance()->Connect();

			}).Then([](const bool connect)
				{
					if (connect)
					{
						if (!NSCommunityServerHandler::GetInstance()->TryFire(NSCommunityServerHandler::EState::Connected))
							return;
					}
					else
					{
						if (!NSCommunityServerHandler::GetInstance()->TryFire(NSCommunityServerHandler::EState::DisConnect))
							return;
					}
				}, *this);
	}
}

void NSGateLogicThread::ProcessExecutor()
{
	{
		std::lock_guard lock(m_ExecutorMutex);
		m_ExecutorBackQueue.swap(m_ExecutorFrontQueue);
	}

	if (!m_ExecutorBackQueue.empty())
	{
		for (const std::function<void()>& function : m_ExecutorBackQueue)
		{
			function();
		}

		m_ExecutorBackQueue.clear();
	}
}


void NSGateLogicThread::Start()
{
	m_Running = true;
	m_MainThread = NSThread(std::bind(&NSGateLogicThread::Process, this));
	m_MainThread.SetDescription("NSGateLogicThread");
}

void NSGateLogicThread::Stop()
{
	m_Running = false;
	//메인쓰레드가 종료 될때까지 대기한다.
	if (m_MainThread.IsRunning())
	{
		m_MainThread.Wait();
	}

	m_ProcessHandlerList.clear();
}

void NSGateLogicThread::AddHandler(INetworkHandler* addHandler)
{
	m_ProcessHandlerList.push_back(addHandler);
}

uint64_t NSGateLogicThread::GetCurrentTick() const
{
	if (m_CurrentTick <= 0)
	{
		// kjw : 초기화!
		//m_CurrentTick = NSTickManager::GetInstance()->GetUnixTimeStamp();
		LOGW << "NSGateLogicThread::m_currentTick is not prepared yet!";
		return NSTickManager::GetInstance()->GetTickMS();
	}

	return m_CurrentTick;
}

bool NSGateLogicThread::RunningInThisThread() const
{
	return m_ThreadId == std::this_thread::get_id();
}

void NSGateLogicThread::Post(const std::function<void()>& function)
{
	std::lock_guard lock(m_ExecutorMutex);
	m_ExecutorFrontQueue.push_back(function);
}
