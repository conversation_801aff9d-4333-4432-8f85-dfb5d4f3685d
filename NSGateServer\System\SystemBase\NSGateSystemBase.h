#pragma once

#include "NSClassTypeId.h"

class NSGateModule;
class NSGateSystemBase
{
public:
	NSGateSystemBase() = delete;
	NSGateSystemBase(NSGateModule* gateModule);
	virtual ~NSGateSystemBase() = default;

	virtual bool Init() = 0;
	virtual bool Reset() = 0;
	virtual void Process() {};

	template<typename T>
	T* GetGateModule()
	{
		return dynamic_cast<T*>(m_GateModule);
	}
	
protected:
	NSGateModule* m_GateModule;
};

namespace NSClassTypeId
{
	template <typename T>
	int SystemTypeId()
	{
		return static_cast<int>(BaseClass<NSGateSystemBase>::GetTypeId<T>());
	}
}

#define INSERT_PROCESSOR_INTERVAL(function, type, interval)\
m_GateModule->RegisterProcessor<type>(std::bind(&function, this, std::placeholders::_1, std::placeholders::_2), interval)

#define INSERT_PROCESSOR(function, type)\
INSERT_PROCESSOR_INTERVAL(function, type, 0)
