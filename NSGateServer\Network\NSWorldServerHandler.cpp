#include "stdafx.h"

#include "Packet/NSPacketPool.h"
#include "NPModels.h"
#include "NPPacket.h"

#include "ServerMain/NSServerMain.h"
#include "GameServer/NSWorldServerManager.h"

#include "NSWorldServerSessionManager.h"
#include "NSClientSessionManager.h"

#include "NSGatePacketRouter.h"

#include "NSUtil/NSTokenizer.h"

#include "Module/NSGateLoginModule.h"
#include "Module/NSGateModuleManager.h"

#include "WaitReconnect/NSWaitReconnectManager.h"

#include "NSClientHandler.h"
#include "NSCommunityServerHandler.h"
#include "NSWorldServerHandler.h"

#include "Data/NSWorldTemplate.h"
#include "Logger/NSLoggerBuidler.h"

NSWorldServerHandler::NSWorldServerHandler()
{
	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketRegisterServerReq, NSPacketRegisterServerReq);
	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketCellIndexSync, NSPacketCellIndexSyncNtf);
	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketPlayerInfoNtf, NSPacketPlayerInfoNtf);
	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketCloseToWorldAck, NSPacketCloseToWorldAck);
	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketWorldLoginAck, NSPacketWorldLoginAck);
	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketSelectedCharacterAck, NSPacketSelectedChracterAck);

	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketCharacterDefaultInfoAck, NSPacketCharacterDefaultInfoAck);

	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketPrepareToMoveServerReq, NSPacketPrepareToMoveServerReq);
	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketMoveServerReq, NSPacketMoveServerReq);
	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketMoveServerAck, NSPacketMoveServerAck);

	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketMoveToCharacterModuleReq, NSPacketMoveToCharacterModuleReq);

	REGISTER_PACKET_HANDLER(NSWorldServerHandler::PacketWorldServerSessionCountNtf, NSPacketWorldServerSessionCountNtf);
}

NSWorldServerHandler::~NSWorldServerHandler()
{
	for (NSPacketData* packetData : m_ReceivedPackets)
	{
		NSPacketPool::GetInstance()->ReceiveDeAlloc(packetData);
	}
	m_ReceivedPackets.clear();
}

void NSWorldServerHandler::OnClose(int64_t socketChannelId, const std::string& error)
{
	LOGD << std::format("OnClose SocketChannelID: {} ({})", socketChannelId, error);

	std::shared_ptr<NSWorldServerSession> session = NSWorldServerSessionManager::GetInstance()->GetSession(socketChannelId);
	if (session != nullptr)
	{
		LOGE << std::format("msg=Connection to the server has been lost, SessionType=GameServer, SocketChannelID={}, IP={}, reason={}"
			, socketChannelId, session->GetChannel()->GetIpAddress(), error);
	}
	m_ClosedSessionIDs.push(socketChannelId);
}

void NSWorldServerHandler::AddConsoleCommand(const wchar_t* consoleCommand)
{
	if (consoleCommand == nullptr || consoleCommand[0] == L'\0')
		return;

	m_ConsoleCommands.push(consoleCommand);
}

void NSWorldServerHandler::Process()
{
	Dispatch_Net_Events();
	ProcessAccept();
	ProcessRecvPacket();
}

void NSWorldServerHandler::PostProcess()
{
	ProcessClose();
	ProcessConsoleCommand();
}

void NSWorldServerHandler::ProcessAccept()
{
	std::shared_ptr<NSWorldServerSession> session = nullptr;

	while (m_AcceptedSessions.try_pop(session))
	{
		if (!NSWorldServerSessionManager::GetInstance()->AddSession(session))
		{
			LOGE << std::format("Duplication WorldServerSession SocketChannelID [{}]", session->GetSocketChannelId());
			if (session->GetChannel() != nullptr)
			{
				session->GetChannel()->PostCloseTask();
			}
			continue;
		}
		LOGI << std::format("msg=Connection to the server was successful, SessionType=GameServer, SocketChannelID={}, IP={}", session->GetSocketChannelId(), session->GetIP());
	}
}

void NSWorldServerHandler::ProcessClose()
{
	int64_t socketChannelId = 0;
	while (m_ClosedSessionIDs.try_pop(socketChannelId))
	{
		std::shared_ptr<NSWorldServerSession> session = NSWorldServerSessionManager::GetInstance()->RemoveSession(socketChannelId);
		if (session != nullptr)
		{
			if (session->GetChannel())
			{
				LOGI << "WorldServer Close Channel ID: " << session->GetChannel()->GetChannelID();
				NSClientSessionManager::GetInstance()->CloseAllSession(session->GetSocketChannelId());

				// kjw : Socket connect후 Register가 안된상태에서 Close되었을때 처리.
				if (session->GetWorldServer() != nullptr)
				{
					session->GetWorldServer()->DelWorldServerSession(session->GetChannel()->GetChannelID());
				}
				session->GetChannel()->PostCloseTask();
			}
		}
	}
}

void NSWorldServerHandler::ProcessRecvPacket()
{
	for (NSPacketData* packetData : m_ReceivedPackets)
	{
		NSPacket* serverPacket = reinterpret_cast<NSPacket*>(packetData->GetBuffer());

		const uint16_t packetType = serverPacket->GetType();
		const int64_t socketChannelId = packetData->GetSocketChannelID();

		std::shared_ptr<NSWorldServerSession> worldServerSession = NSWorldServerSessionManager::GetInstance()->GetSession(socketChannelId);
		if (worldServerSession == nullptr)
		{
			continue;
		}

		if (m_PacketHandler.find(packetType) != m_PacketHandler.end())
		{
			m_PacketHandler[packetType](worldServerSession, packetData->GetBuffer());
		}
		else if (EServerPacketType::Begin < packetType && packetType < EServerPacketType::End)
		{
			switch (serverPacket->GetPacketDestination())
			{
			case EServerPacketDestination::Community:
			{
				const std::shared_ptr<NSCommunityServerSession> communitySession = NSCommunityServerHandler::GetInstance()->GetSession(serverPacket->GetSessionId());
				if (communitySession != nullptr)
				{
					communitySession->Send(packetData->GetBuffer(), packetData->GetSize());
				}
				break;
			}
			case EServerPacketDestination::Game:
			{
				const std::shared_ptr<NSWorldServer> worldServer = NSWorldServerManager::GetInstance()->GetWorldServer(serverPacket->GetDestEndPointSID());
				if (worldServer != nullptr)
				{
					worldServer->SendNoSpecifySession(packetData->GetBuffer(), packetData->GetSize());
				}
				break;
			}
			default:
				LOGE << std::format("Unhandled PacketDestination [{}]", static_cast<int32_t>(serverPacket->GetPacketDestination()));
				break;
			}
			continue;
		}
		else
		{
			NPPacket* publicPacket = reinterpret_cast<NPPacket*>(packetData->GetBuffer());
			const std::string packetName = NPPacketType_1000_GetPacketName(packetType);

			//게이트 서버에 세션이 없어도 BroadCast 진행.
			if (publicPacket->IsGateBroadCast())
			{
				std::shared_ptr<NSWorldServer> worldServer = worldServerSession->GetWorldServer();
				const int64_t worldModuleId = publicPacket->GetMapModuleId();
				const int32_t worldCellIndex = publicPacket->GetIndex();
				const int32_t worldCellIndexGap = publicPacket->GetIndexGap();

				auto cellBroadcast = [&](int32_t index) -> bool
					{
						const NSWorldMapCell* worldMapCell = worldServer->FindWorldMapCell(worldModuleId, index);
						if (worldMapCell == nullptr)
							return false;

						for (const int64_t otherSessionID : worldMapCell->GetWorldMapPlayerList())
						{
							auto otherSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(otherSessionID);
							if (otherSession != nullptr)
							{
								if (publicPacket->GetGateBroadCastExceptEntityId() == otherSession->GetWorldEntityId())
									continue;
								otherSession->BypassSend(packetData->GetBuffer());
							}
						}
						return true;
					};

				//Cell BroadCast 
				// 0 | 1 | 2        0 | 1         1
				//---+---+---  or  ---+---   or  ---  or  1 | 2 | 3 | 4
				// 3 | 4 | 5        2 | 3         2
				//---+---+---      ---+---       ---
				// 6 | 7 | 8        4 | 5         3

				int32_t index = -1;

				if (worldCellIndexGap == -1)
				{
					//해당 Cell에만 BroadCast
					cellBroadcast(worldCellIndex);
				}
				else if (worldCellIndexGap >= 3)
				{
					for (int32_t i = 0; i < MaxNeighborCell; ++i)
					{
						if (i <= 2)
						{
							index = worldCellIndex - worldCellIndexGap + i - 1;
						}
						else if (i > 2 && i <= 5)
						{
							index = worldCellIndex + i - 4;
						}
						else if (i > 5)
						{
							index = worldCellIndex + worldCellIndexGap + i - 7;
						}
						cellBroadcast(index);
					}
				} //2 이하는 거의 있을 수 없는 일.
				else if (worldCellIndexGap == 2)
				{
					for (int32_t i = 0; i < 6; ++i)
					{
						if (worldCellIndex % 2 == 1)
						{
							index = worldCellIndex + (i / 2 - 1) * worldCellIndexGap + (i % 2);
						}
						else
						{
							index = worldCellIndex + (i / 2 - 1) * worldCellIndexGap + (i % 2 - 1);
						}
						cellBroadcast(index);
					}
				}
				else if (worldCellIndexGap <= 1)
				{
					for (int32_t i = 0; i < 3; i++)
					{
						index = worldCellIndex + i - 1;
						cellBroadcast(index);
					}
				}
				continue;
			}

			std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(publicPacket->GetGateSessionid());
			if (clientSession == nullptr)
			{
				LOGE << "Error! ClientSession is nullptr";
				continue;
			}

			if (clientSession->GetModuleID() == static_cast<int32_t>(EGateModule::MoveServer))
			{
				LOGD << std::format("Client Send MoveServer Module Packet SessionId: {}, Name: {}, Code: {}, Size: {}/Byte",
					clientSession->GetSessionId(), packetName, packetType, publicPacket->GetSize());

				if (packetType == EPacketType_1000::ePacketType_RegionMoveAck)
				{
					NSGatePacketRouter::GetInstance()->MoveSession(clientSession, EGateModule::World);
				}
				//				서버이동시에도 클라이언트가 받아야 할 패킷들이 있기때문에 주석처리
				//				else
				//				{
				//					LOGD << std::format("return Client Send MoveServer Module Packet  [Name : {}], [Code : {}], [Size : {}/Byte]", packetName, packetType, publicPacket->GetSize());
				//					continue;
				//				}
			}

			if (packetType != EPacketType_1000::ePacketType_HeartbeatAck)
			{
				LOGD << std::format("Game Server Send Packet SessionId: {}, Name: {}, Code: {}, Size: {}/Byte",
					clientSession->GetSessionId(), packetName, packetType, publicPacket->GetSize());
			}
			clientSession->BypassSend(packetData->GetBuffer());
		}
	}

	for (NSPacketData* packetData : m_ReceivedPackets)
	{
		NSPacketPool::GetInstance()->ReceiveDeAlloc(packetData);
	}
	m_ReceivedPackets.clear();
}

void NSWorldServerHandler::ProcessConsoleCommand()
{
	std::wstring commandReader;
	while (m_ConsoleCommands.try_pop(commandReader))
	{
		NSTokenizer tokens(commandReader.c_str(), L' ');

		const wchar_t* command = tokens.Get(0, L"");
		if (0 == _wcsicmp(command, L"exit"))
		{
			g_pcServerMain->SetServerState(ESERVER_STATE::RELEASE);
		}
		else if (0 == _wcsicmp(command, L"crash"))
		{
			LOGF << "Crashed by Console Command.";
			int32_t* crash = (int32_t*)0xFFFFFFFFFFFFDEAD;
			*crash = 0xDEAD;
		}
		else if (0 == _wcsicmp(command, L"actionlog"))
		{
			NSKGLogger logger(LOG_ID_KG_ACTION, 1, NSKGLogger::LogType::Action, "800831");

			logger.field("grade", 11);
			logger.field("category", "tutorial");
			logger.field("action", "skip");

			LOGI << "Dummy action log generated.";
		}
		else
		{
			LOGI << "Unknown command.\nAvailable commands:\nexit: Exit server gracefully.\ncrash: Crash server for test purpose.\nactionlog: Generating dummy action log for test purpose";
		}
	}
}

//void NSWorldServerHandler::RegisterPacketHandler(std::function<void(std::shared_ptr<NSWorldServerSession>&, NSPacket*)> func, EServerPacketType type)
//{
//	if (EServerPacketType::End <= type)
//	{
//		LOGE << std::format("RegisterPacketHandler Failed. Invalid PacketType [{}]", static_cast<int32_t>(type));
//		return;
//	}
//	m_PacketHandler[static_cast<uint16_t>(type)] = func;
//}

void NSWorldServerHandler::ProcessMonitor()
{
}

void NSWorldServerHandler::ForwardedEventAccept(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)
{
	/*
	어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	NSWorldServerHandler::ForwardedEventAccept <- we are here
	NSWorldServerServiceLogicAdapter::ForwardAcceptEventIntoThis
	CNLIOInterfaceAdapter::OnAccept
	CNLNetEventQueue::Dispatch_Queued_Socket_Accepted
	CNLNetEventQueue::Dispatch_Net_Events
	NSClientHandler::Process
	OnAccept 가 하던 역할을 어댑터 함수가 처리합니다.
	*/

	if (socketInterface == nullptr)
		return;

	const auto session = std::make_shared<NSWorldServerSession>();
	if (session == nullptr)
	{
		socketInterface->Close(nullptr);
		return;
	}
	session->SetChannel(socketInterface);
	m_AcceptedSessions.push(session);
}

void NSWorldServerHandler::ForwardedEventRecvPacket(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)
{
	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSWorldServerHandler::ForwardedEventRecvPacket <- we are here
	//NSWorldServerServiceLogicAdapter::ForwardRecvPacketEventIntoThis
	//CNLIOInterfaceAdapter::OnRecvPacket
	//CNLNetEventQueue::Dispatch_Queued_Packet_Arrived
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process
	//OnReceive 가 하던 역할을 어댑터 함수가 처리합니다.

	if (buffer == nullptr)
	{
		LOGE << "PacketBuffer IS NULL";
		return;
	}

	NSPacketData* packetData = nullptr;
	if (allocateForCopy)
	{
		packetData = NSPacketPool::GetInstance()->ReceiveAlloc(size);
		if (packetData == nullptr)
		{
			LOGE << "PacketData IS NULL";
			return;
		}

		if (packetData->GetSize() < size)
		{
			NSPacketPool::GetInstance()->ReceiveDeAlloc(packetData);
			LOGE << "PacketData Size Over";
			return;
		}
		memcpy(packetData->GetBuffer(), buffer, size);
	}
	else
	{
		//param buffer 가 NetEventBuffer 의 영역인 경우
		//해당 영역은 서비스로직에서 다음 틱이 시작될 때까지 안전합니다.
		//NSPacketData 객체만 할당하고, 스트림을 복사하지 않아도 됩니다.
		packetData = new NSPacketData(buffer, size, true, true);
		if (packetData == nullptr)
		{
			LOGE << "Allocate for NSPacketData(" << sizeof(NSPacketData) << ") byte failed";
			return;
		}
	}

	packetData->SetSocketChannelID(static_cast<int64_t>(socketInterfaceId));
	m_ReceivedPackets.push_back(packetData);
}

void NSWorldServerHandler::ForwardedEventClose(int64_t socketInterfaceId, [[maybe_unused]] int32_t nativeErrorCode, const std::string& why)
{
	//어댑터에서 호출 될 경우 콜스택이 다음과 같습니다.
	//NSWorldServerHandler::ForwardedEventClose <- we are here
	//NSWorldServerServiceLogicAdapter::ForwardCloseEventIntoThis 
	//CNLIOInterfaceAdapter::OnDisconnected
	//CNLNetEventQueue::Dispatch_Queued_Socket_Disconnected
	//CNLNetEventQueue::Dispatch_Net_Events
	//NSClientHandler::Process
	//OnClose는 그대로 사용되기 때문에, 인자를 맞춰서 전달합니다.

	OnClose(socketInterfaceId, why.c_str());
}

void NSWorldServerHandler::PacketRegisterServerReq(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	const NSPacketRegisterServerReq* req = static_cast<NSPacketRegisterServerReq*>(packet);

	std::shared_ptr<NSWorldServer> findWorldServer = NSWorldServerManager::GetInstance()->GetWorldServer(req->GetSID());
	if (findWorldServer == nullptr)
	{
		// 서버 등록
		std::shared_ptr<NSWorldServer> newWorldServer = std::make_shared<NSWorldServer>();
		if (NSWorldServerManager::GetInstance()->AddWorldServer(req->GetSID(), req->GetWorldServerType(), newWorldServer))
		{
			NSPacketRegisterServerAck cSend;
			cSend.SetSID(req->GetSID());
			session->Send(cSend);

			newWorldServer->AddWorldServerSession(session->GetChannel()->GetChannelID(), session);
			newWorldServer->SetServerInfo({ req->GetSID(), req->GetWorldServerType(), req->GetPublicEndPoint() });
			newWorldServer->ResetClientSessionCount();
			session->SetWorldServer(newWorldServer);

			//로드가 안됐을때만 요청
			if (!NSWorldServerManager::GetInstance()->CharacterDefaultInfoLoaded())
			{
				if (EWorldServerType::None != (newWorldServer->GetWorldServerType() & EWorldServerType::Persistant))
				{
					NSPacketCharacterDefaultInfoReq defReq;
					session->Send(defReq);
				}
			}
			LOGI << std::format("msg=GameServer is now accessible to users, ServerType={}, SocketChannelID={}, IP={}, SID={}"
				, to_string(newWorldServer->GetWorldServerType()), session->GetSocketChannelId(), session->GetIP(), req->GetSID());

			NSWorldServerManager::GetInstance()->SortWorldServer(req->GetWorldServerType());
		}
		else
		{
			NSPacketRegisterServerAck cAck;
			cAck.SetResult(EServerErrorType::LINKSERVER_DUPPLICATE);
			session->Send(cAck);
			session->GetChannel()->Close(nullptr);
		}
	}
	else
	{
		findWorldServer->AddWorldServerSession(session->GetChannel()->GetChannelID(), session);
		findWorldServer->SetServerInfo({ req->GetSID(), req->GetWorldServerType(), req->GetPublicEndPoint() });
		findWorldServer->ResetClientSessionCount();
		session->SetWorldServer(findWorldServer);

		LOGI << std::format("msg=GameServer is now accessible to users, ServerType={}, SocketChannelID={}, IP={}, SID={}"
			, to_string(findWorldServer->GetWorldServerType()), session->GetSocketChannelId(), session->GetIP(), req->GetSID());
	}
}

void NSWorldServerHandler::PacketCellIndexSync(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	const auto recvPacket = static_cast<NSPacketCellIndexSyncNtf*>(packet);
	const auto clientSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(recvPacket->GetSessionId());
	if (clientSession == nullptr)
		return;

	if (clientSession->GetWorldMapCell() != nullptr)
	{
		clientSession->GetWorldMapCell()->DelWorldMapPlayer(clientSession->GetSessionId());
	}

	NSWorldMapCell* ret = session->GetWorldServer()->SetWorldMapCell(recvPacket->GetModuleId(), recvPacket->GetCellIndex(), packet->GetSessionId());
	if (ret != nullptr)
	{
		clientSession->SetWorldMapCell(ret);
		clientSession->SetWorldModuleId(recvPacket->GetModuleId());
		clientSession->SetWorldCellIndex(recvPacket->GetCellIndex());
	}
	else
	{
		assert(false);
	}
}

void NSWorldServerHandler::PacketPlayerInfoNtf([[maybe_unused]] std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	const NSPacketPlayerInfoNtf* req = static_cast<NSPacketPlayerInfoNtf*>(packet);
	const std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(req->GetSessionId());
	if (clientSession == nullptr)
		return;

	clientSession->SetWorldEntityId(req->GetEntityID());
}

void NSWorldServerHandler::PacketCloseToWorldAck([[maybe_unused]] std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	const NSPacketCloseToWorldAck* ack = static_cast<NSPacketCloseToWorldAck*>(packet);
	const std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(ack->GetSessionId());

	if (clientSession == nullptr)
		return;

	NSResultRemoveSession resultRemoveSession = ack->GetResultRemoveSession();
	if (resultRemoveSession.WaitReconnect)
	{
		NSWaitReconnectManager::GetInstance()->AddWaitReconnect(clientSession->GetAID(), resultRemoveSession.WaitReconnectContext);

		auto communitySession = NSCommunityServerHandler::GetInstance()->GetSession(clientSession->GetSessionId());
		if (communitySession != nullptr)
		{
			NSPacketPacketDisconnectWaitReconnectNtf ntf;
			ntf.SetCid(resultRemoveSession.WaitReconnectContext.Cid);
			communitySession->Send(ntf);
		}
	}
	else
	{
		auto communitySession = NSCommunityServerHandler::GetInstance()->GetSession(clientSession->GetSessionId());
		if (communitySession != nullptr)
		{
			NSPacketLogoutSyncToCommunityServerNtf ntf;
			ntf.SetAID(clientSession->GetAID());
			communitySession->Send(ntf);
		}
	}

	auto removeSession = NSClientSessionManager::GetInstance()->RemoveSession(clientSession->GetSocketChannelId());
	if (removeSession != nullptr)
	{
		NSGatePacketRouter::GetInstance()->CloseSession(clientSession->GetSocketChannelId());
		if (clientSession->GetSocketChannel())
		{
			if (clientSession->GetWorldMapCell() != nullptr)
			{
				clientSession->GetWorldMapCell()->DelWorldMapPlayer(clientSession->GetSessionId());
			}

			LOGI << std::format("Client Close SessionId:[{}], channelId:[{}] ", clientSession->GetSessionId(), clientSession->GetSocketChannelId());
			clientSession->GetSocketChannel()->PostCloseTask();
		}
	}
}

void NSWorldServerHandler::PacketWorldLoginAck([[maybe_unused]] std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	const NSPacketWorldLoginAck* ack = static_cast<NSPacketWorldLoginAck*>(packet);
	std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(packet->GetSessionId());

	NPPacketSelectCharacterReq req;
	req.SetCID(ack->GetCID());
	req.SetGateClientSessionId(clientSession->GetSessionId());

	if (clientSession->WorldServerNPSend(&req))
	{
		LOGI << std::format("Success WorldServerNPSend : socket ChannelId : {}, cid : {}", clientSession->GetSessionId(), req.GetCID());
	}
	else
	{
		assert(false);
		return;
	}
}

void NSWorldServerHandler::PacketSelectedCharacterAck([[maybe_unused]] std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	const NSPacketSelectedChracterAck* ack = static_cast<NSPacketSelectedChracterAck*>(packet);
	std::shared_ptr<NSClientSession> clientSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(packet->GetSessionId());

	if (clientSession == nullptr)
		return;

	if (ack->GetSelectedCharacterResult() == EErrorCode::None)
	{
		NPPacketSelectCharacterAck result;
		result.SetCID(ack->GetCID());
		result.SetResult(EErrorCode::None);
		clientSession->Send(result);

		NSGatePacketRouter::GetInstance()->MoveSession(clientSession, EGateModule::World);

		NSWaitReconnectManager::GetInstance()->RemoveWaitReconnect(clientSession->GetAID());
	}
	else
	{
		NPPacketCommonNtf ntf;
		ntf.SetResult(ack->GetSelectedCharacterResult());
		session->Send(ntf);
	}
}

void NSWorldServerHandler::PacketCharacterDefaultInfoAck(std::shared_ptr<NSWorldServerSession>&, NSPacket* packet)
{
	const NSPacketCharacterDefaultInfoAck* ack = static_cast<NSPacketCharacterDefaultInfoAck*>(packet);

	//로드된적 있는 정보
	if (NSWorldServerManager::GetInstance()->GetCharacterDefaultInfo(ack->GetClassType()).DefaultChronotectorNodeId != 0)
		return;

	NSCharacterDefaultInfo info{
		.DefaultChronotectorNodeId = ack->GetDefaultChronotectorNodeId(),
		.NodePoint = ack->GetNodePoint(),
		.DefaultCharacterGoods = ack->GetDefaultCharacterGoods(),
		.DefaultClasses = ack->GetDefaultClasses(),
		.DefaultEquipmentJson = ack->GetDefaultEquipmentJson(),
		.DefaultMainQuestJson = ack->GetDefaultMainQuestJson(),
		.DefaultWeaponMastery = ack->GetDefaultWeaponMastery(),
		.DefaultChronotectorActionJson = ack->GetDefaultChronotectorActionJson(),
		.RewardPayload = ack->GetRewardPayload()
	};
	NSWorldServerManager::GetInstance()->SetCharacterDefaultInfo(ack->GetClassType(), info);
}

//서버 이동전 패킷처리를 막기위해 무브서버 모듈로 이동
void NSWorldServerHandler::PacketPrepareToMoveServerReq(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	const NSPacketPrepareToMoveServerReq* req = static_cast<NSPacketPrepareToMoveServerReq*>(packet);

	auto pcClientSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(req->GetMoveInfo().ClientSessionId);
	if (pcClientSession == nullptr)
		return;

	//모듈이동 실패, 에러 처리 필요
	if (!NSGatePacketRouter::GetInstance()->MoveSession(pcClientSession, EGateModule::MoveServer))
	{
		NSClientHandler::GetInstance()->OnClose(req->GetMoveInfo().ClientSessionId, "NSWorldServerSession::PacketPrepareToMoveServerReq");
		LOGE << "Module change fail";
		return;
	}

	//모듈 이동시키는대 성공했다고 처리
	NSPacketPrepareToMoveServerAck ack;
	ack.SetMoveInfo(req->GetMoveInfo());
	session->Send(ack);
}

//캐릭터 / 맵 생성요청
void NSWorldServerHandler::PacketMoveServerReq(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	NSPacketMoveServerReq* req = static_cast<NSPacketMoveServerReq*>(packet);

	auto pcFromServer = session->GetWorldServer();
	if (pcFromServer == nullptr)
		return;

	std::shared_ptr<NSWorldServer> pcTargetServer = nullptr;
	NSMoveServerInfo newMoveServerInfo = req->GetMoveInfo();

	//목적지가 있을경우(파티원 소환 / 채널이동같은 경우)
	if (0 < newMoveServerInfo.TargetSid)
	{
		pcTargetServer = NSWorldServerManager::GetInstance()->GetWorldServer(newMoveServerInfo.TargetSid);
	}
	else
	{
		//리전 정보가 없다
		auto pcRegion = NSWorldTemplate::GetInstance()->FindRegion(newMoveServerInfo.TargetRegionId);
		if (pcRegion == nullptr)
		{
			NSClientHandler::GetInstance()->OnClose(newMoveServerInfo.ClientSessionId, "NSWorldServerSession::PacketMoveServerReq");
			return;
		}

		pcTargetServer = NSWorldServerManager::GetInstance()->FindWorldServerByType(pcRegion->RegionType);
	}

	//서버를 찾을수 없다
	if (pcTargetServer == nullptr)
	{
		NSClientHandler::GetInstance()->OnClose(newMoveServerInfo.ClientSessionId, "NSWorldServerSession::PacketMoveServerReq");
		LOGE << "NOT FOUND TARGET SERVER!!";
		return;
	}

	newMoveServerInfo.PrevSid = pcFromServer->GetServerInfo().m_Sid;
	newMoveServerInfo.TargetSid = pcTargetServer->GetServerInfo().m_Sid;
	req->SetMoveInfo(newMoveServerInfo);

	pcTargetServer->SendNoSpecifySession(*req);
}

//생성 / 이동 완료 후 처리
void NSWorldServerHandler::PacketMoveServerAck(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet)
{
	NSPacketMoveServerAck* ack = static_cast<NSPacketMoveServerAck*>(packet);

	auto pcClientSession = NSClientSessionManager::GetInstance()->GetSessionByAID(ack->GetAID());
	if (pcClientSession == nullptr)
		return;

	auto pcPrevServer = NSWorldServerManager::GetInstance()->GetWorldServer(ack->GetPrevSid());
	if (pcPrevServer == nullptr)
		return;

	if (ack->GetResult() != EServerErrorType::None)
	{
		LOGE << std::format("MoveServerAck Failed, ClientSocketChannelID[{}], ErrorCode[{}]", pcClientSession->GetSocketChannelId(), static_cast<int32_t>(ack->GetResult()));
		NSClientHandler::GetInstance()->OnClose(pcClientSession->GetSocketChannelId(), "NSWorldServerSession::PacketMoveServerAck");
		return;
	}

	pcPrevServer->SendNoSpecifySession(*ack);
	NSGatePacketRouter::GetInstance()->MoveSession(pcClientSession, EGateModule::World);
	pcClientSession->SetWorldServer(session->GetWorldServer());
}

void NSWorldServerHandler::PacketMoveToCharacterModuleReq(std::shared_ptr<NSWorldServerSession>&, NSPacketMoveToCharacterModuleReq* req)
{
	auto pcClientSession = NSClientSessionManager::GetInstance()->GetSessionBySessionId(req->GetClientSessionId());
	if (pcClientSession == nullptr)
		return;

	auto communitySession = NSCommunityServerHandler::GetInstance()->GetSession(pcClientSession->GetSessionId());
	if (communitySession != nullptr)
	{
		NSPacketLogoutSyncToCommunityServerNtf ntf;
		ntf.SetAID(pcClientSession->GetAID());
		communitySession->Send(ntf);
	}

	pcClientSession->SetWorldServer(nullptr);
	NSGatePacketRouter::GetInstance()->MoveSession(pcClientSession, EGateModule::Character);
}

void NSWorldServerHandler::PacketWorldServerSessionCountNtf(std::shared_ptr<NSWorldServerSession>& pcSession, NSPacketWorldServerSessionCountNtf* ntf)
{
	auto pcWorldServer = pcSession->GetWorldServer();
	if (pcWorldServer == nullptr)
		return;

	pcWorldServer->UpdateClientSessionCount(ntf->GetSessionCount());
}