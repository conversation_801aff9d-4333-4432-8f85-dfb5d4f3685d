#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpGetCharacterList : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spGetCharacterList";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t Aid = 0;
		int32_t Wid = 0;
		int64_t NowTick = 0;
	} Input;

	SpGetCharacterList() = default;
	SpGetCharacterList(const int64_t aid, const int32_t wid, const int64_t NowTick);
};
