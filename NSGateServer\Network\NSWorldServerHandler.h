#pragma once

#include "Pool/NSPool.h"
#include "Network/NSWorldServerSession.h"
#include "Packet/NSPacketData.h"
#include "NSSingleton.h"
#include "CNLServiceLogicAdapter.h"
#include "NSWorldServerServiceLogicAdapter.h"

#include "INetworkHandler.h"

class NSWorldServerHandler : public INetworkHandler, public CNLServiceLogicAdapterRef<NSWorldServerServiceLogicAdapter>, public TemplateSingleton<NSWorldServerHandler>
{
public:
	NSWorldServerHandler();
	~NSWorldServerHandler() override;

public:
	void OnClose(int64_t socketChannelId, const std::string& error);

	void ForwardedEventAccept(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)							override;
	void ForwardedEventRecvPacket(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)	override;
	void ForwardedEventClose(int64_t socketInterfaceId, int32_t nativeErrorCode, const std::string& why)				override;

	void Process() override;
	void PostProcess() override;
	void AddConsoleCommand(const wchar_t* consoleCommand);

private:
	void ProcessAccept();
	void ProcessClose();
	void ProcessRecvPacket();
	void ProcessMonitor();
	void ProcessConsoleCommand();

	//void RegisterPacketHandler(std::function<void(std::shared_ptr<NSWorldServerSession>&, NSPacket*)> func, EServerPacketType type);

	template <typename _PacketType>
	void RegisterPacketHandler(std::function<void(std::shared_ptr<NSWorldServerSession>&, _PacketType*&)> processor)
	{
		auto it = m_PacketHandler.find(_PacketType::GetPacketType());
		if (it != m_PacketHandler.end())
		{
			LOGW << std::format("Packet Callback func has been overwrited![PacketType:{}][PacketName:{}]", _PacketType::GetPacketType(), _PacketType::GetPacketName());
		}

		[[maybe_unused]] bool inserted = m_PacketHandler.try_emplace(_PacketType::GetPacketType(), [=, this](std::shared_ptr<NSWorldServerSession>& session, const char* buffer) {
			PacketProcessor<_PacketType>(processor, session, buffer);
			}).second;
		assert(inserted);

#ifdef  _DEBUG
		//LOGD << std::format("Register Packet Callback[PacketType:{}][PacketName:{}]", _PacketType::GetPacketType(), _PacketType::GetPacketName());
#endif
	}

	template <typename _PacketType>
	void PacketProcessor(std::function<void(std::shared_ptr<NSWorldServerSession>&, _PacketType*&)> process, std::shared_ptr<NSWorldServerSession>& session, const char* buffer)
	{
		_PacketType packet;
		_PacketType* packetPtr;
		if (packet.Deserialize(buffer))
		{
			packetPtr = &packet;
		}
		else
		{
			packetPtr = (_PacketType*)buffer;

			if (packetPtr->GetSize() != packet.GetSize())
			{
				LOGW << std::format("ReceivePacket Size different [Type:{}] [ReceiveSize:{}] [PacketSize:{}]", packetPtr->GetType(), packetPtr->GetSize(), packet.GetSize());
				return;
			}
		}

		process(session, packetPtr);
	}

private:
	static void PacketRegisterServerReq(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);
	static void PacketCellIndexSync(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);
	static void PacketPlayerInfoNtf(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);
	static void PacketCloseToWorldAck(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);
	static void PacketWorldLoginAck(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);
	static void PacketSelectedCharacterAck(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);

	static void PacketCharacterDefaultInfoAck(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);

	static void PacketPrepareToMoveServerReq(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);
	static void PacketMoveServerReq(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);
	static void PacketMoveServerAck(std::shared_ptr<NSWorldServerSession>& session, NSPacket* packet);

	static void PacketMoveToCharacterModuleReq(std::shared_ptr<NSWorldServerSession>& session, NSPacketMoveToCharacterModuleReq* packet);

	static void PacketWorldServerSessionCountNtf(std::shared_ptr<NSWorldServerSession>& session, NSPacketWorldServerSessionCountNtf* packet);

private:
	using PROCESSHANDLE = std::function<void(std::shared_ptr<NSWorldServerSession>&, const char*)>;

private:
	concurrency::concurrent_queue<std::shared_ptr<NSWorldServerSession>> m_AcceptedSessions;
	concurrency::concurrent_queue<int64_t> m_ClosedSessionIDs;
	concurrency::concurrent_queue<std::wstring>	m_ConsoleCommands;

	std::vector<NSPacketData*> m_ReceivedPackets;

	std::unordered_map<uint16_t, PROCESSHANDLE> m_PacketHandler;
};

#define REGISTER_PACKET_HANDLER(function, type)\
RegisterPacketHandler<type>(function);