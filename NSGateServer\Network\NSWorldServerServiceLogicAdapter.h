#pragma once
#include "CNLServiceLogicAdapter.h"


class NSWorldServerServiceLogicAdapter : public CNLServiceLogicAdapter
{
public:
	NSWorldServerServiceLogicAdapter() {}
	virtual ~NSWorldServerServiceLogicAdapter() {}

public:
	virtual void ForwardAcceptEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)																						override;
	virtual void ForwardRecvPacketEventIntoThis(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)																override;
	virtual void ForwardCloseEventIntoThis(int64_t socketInterfaceId, int32_t nativeErrorCode, const std::string& why)																			override;
	virtual void ForwardConnectSuccessEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port)									override;
	virtual void ForwardConnectFailEventIntoThis(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface, const std::string& ip, unsigned short port, int32_t nativeErrorCode, const std::string& why)	override;

};