#pragma once

#include "Network/NSClientSession.h"
#include "Packet/NSPacketData.h"

#include "CNLServiceLogicAdapter.h"
#include "NSClientServiceLogicAdapter.h"

#include "INetworkHandler.h"

class NSClientHandler : public INetworkHandler, public CNLServiceLogicAdapterRef<NSClientServiceLogicAdapter>, public TemplateSingleton<NSClientHandler>
{
public:
	NSClientHandler();
	~NSClientHandler() override;

public:
	void OnClose(int64_t socketChannelId, const std::string& error);
	void Process() override;
	void PostProcess() override;

	virtual void ForwardedEventAccept(const std::shared_ptr<CNLIOInterfaceAdapter>& socketInterface)													override;
	virtual void ForwardedEventRecvPacket(int64_t socketInterfaceId, const char* buffer, int32_t size, bool allocateForCopy)							override;
	virtual void ForwardedEventClose(int64_t socketInterfaceId, int32_t nativeErrorCode, const std::string& why)										override;
public:
	void ProcessAccept();
	void ProcessClose();
	void ProcessRecvPacket();
	void ProcessGameLogic();

private:
	concurrency::concurrent_queue<std::shared_ptr<NSClientSession>>	m_AcceptSessionQueue;
	concurrency::concurrent_queue<int64_t> m_CloseSessionQueue;
	std::vector<NSPacketData*> m_ReceivePacketQueue;
};
