#include "stdafx.h"
#include "SpGetCharacterRegionId.h"

#include "ADO/NSAdoCommand.h"

SpGetCharacterRegionId::SpGetCharacterRegionId(const int64_t Aid, const int64_t Cid) : Input(Aid, Cid)
{

}

EErrorCode SpGetCharacterRegionId::Make<PERSON><PERSON>y(NSAdoCommand* command)
{
	command->SetItem("AID", Input.Aid);
	command->SetItem("CID", Input.Cid);
	return EErrorCode::None;
}

EErrorCode SpGetCharacterRegionId::MakeOutput(NSAdoCommand* command)
{
	command->GetItem("RegionId", Output.RegionId);
	return EErrorCode::None;
}