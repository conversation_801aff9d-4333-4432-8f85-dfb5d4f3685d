#pragma once

class NPPacket;
class NSPacket;
class NSPacketData;
class CNLIOInterfaceAdapter;

class NSCommunityServerSession : public std::enable_shared_from_this<NSCommunityServerSession>
{
public:
	NSCommunityServerSession();
	virtual ~NSCommunityServerSession();

public:
	void Reset();
	auto GetSocketChannelId() const -> int64_t;
	void SetChannel(const std::shared_ptr<CNLIOInterfaceAdapter>& channel);
	auto GetChannel() const -> const std::shared_ptr<CNLIOInterfaceAdapter>&;

	void Send(const char* buffer, int size);
	template <typename T>
	void Send(T& packet)
	{
		static_assert(!std::is_same_v<T, NSPacket>, "NSPacket is not allowed");

		if (packet.NeedSerialize())
		{
			static char* localBuffer = new char[g_uMaxPacketSize];
			if (!packet.Serialize(localBuffer))
			{
				assert(false);
				return;
			}
			Send(localBuffer, packet.GetSize());
		}
		else
		{
			Send((char*)&packet, packet.GetSize());
		}
	}

	void Close() const;

public:
	void OnRecv(NSPacket* packet, NSPacketData* packetData);

	void RegisterPacketHandler(std::function<void(std::shared_ptr<NSCommunityServerSession>&, NSPacket*)> func, uint16_t type);

private:
	std::shared_ptr<CNLIOInterfaceAdapter> m_SocketChannel = nullptr;
	using PROCESSHANDLE = std::function<void(std::shared_ptr<NSCommunityServerSession>&, NSPacket*)>;
	static std::unordered_map<uint16_t, PROCESSHANDLE> m_PacketHandler;
};
