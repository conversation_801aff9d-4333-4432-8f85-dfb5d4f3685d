#include <CNLCoreIO.h>

#define AGENT_MESSAGE_STREAM CNLGlobalPipe<NSGateServerLocalAgentConnection>::GetSingleMessageStream()
#define AGENT_PEER CNLGlobalPipe<NSGateServerLocalAgentConnection>::GetPeer()

namespace json11
{
	class Json;
}

class NSGateServerLocalAgentConnection : public CNLPeer
{
public:
	NSGateServerLocalAgentConnection();
	virtual ~NSGateServerLocalAgentConnection() {}

private:
	void OnRecvPacket(CNLNetEventQueue*, CNLPacketRef& packet) override;
	void OnDisconnected(CNLNetEventQueue*, int32_t nativeErrorCode, const std::string& why) override;
	void OnAccepted(CNLNetEventQueue*) override;
	void OnConnectSucceeded(CNLNetEventQueue* callerQueue, const std::string& ip, unsigned short port) override;
	void OnConnectFailed(CNLNetEventQueue* callerQueue, const std::string& ip, unsigned short port, int32_t nativeErrorCode, const std::string& why) override;

	//________________RECV EVENT HANDLERS________________________
private:
	void OnRecvJson(char* dispatchThis, cnl_packet_size_t len);
	void OnRecvJsonTerminateProcess(json11::Json& jsonPayload);

	//___________________________________________________________

public:
	void ChangeStatus(const std::string& status);

	void SendMessageToLocalAgent(const std::string& message);
	void SendStatusTransitionToLocalAgent();
	void SendHelloToLocalAgent();

	//____________________________________________________________


private:
	virtual void Update(unsigned int elapsedTime);
	bool UpdateReportServerStatus(unsigned int elapsedTime);
	void SendServerStatusToLocalAgent();


private:
	unsigned int m_DelayReport = 1000;
	unsigned int m_ACTimerReportStatus = 0;


	//"BLANK",
	//"BOOT_UP_PROGRESS",
	//"ON_SERVICE",
	//"SHUT_DOWN_SENT",
	//"SHUT_DOWN_PROGRESS",
	//"SHUT_DOWN_COMPLETE",

	std::string	m_MyStatus = "BLANK";


	const static std::unordered_map<std::string, void (NSGateServerLocalAgentConnection::*)(char*, cnl_packet_size_t)> PacketHandlers;
	const static std::unordered_map<std::string, void (NSGateServerLocalAgentConnection::*)(json11::Json&)> JsonPayloadHandlers;

};


