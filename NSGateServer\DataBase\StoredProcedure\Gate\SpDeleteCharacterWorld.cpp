#include "stdafx.h"
#include "SpDeleteCharacterWorld.h"

#include "ADO/NSAdoCommand.h"

SpDeleteCharacterWorld::SpDeleteCharacterWorld(const int32_t wid, const std::string_view platformId, const int32_t deleteSec) : Input{ wid }
{
	strcpy_s(Input.PlatformID, sizeof(Input.PlatformID), platformId.data());
	Input.DeleteSec = deleteSec;
}

EErrorCode SpDeleteCharacterWorld::MakeQuery(NSAdoCommand* command)
{	
	if (!command->SetItem("PlatformID", Input.PlatformID))
		return EErrorCode::DBArgumentError;
	command->SetItem("WID", Input.Wid);
	command->SetItem("DeleteSec", Input.DeleteSec);
	return EErrorCode::None;
}
