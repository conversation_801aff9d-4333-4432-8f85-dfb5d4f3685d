#include "stdafx.h"
#include "SpInsertCharacterWorld.h"

#include "ADO/NSAdoCommand.h"

SpInsertCharacterWorld::SpInsertCharacterWorld(const int32_t wid, const std::string_view platformId) : Input{ wid }
{
	strcpy_s(Input.PlatformID, sizeof(Input.PlatformID), platformId.data());
}

EErrorCode SpInsertCharacterWorld::MakeQuery(NSAdoCommand* command)
{	
	if (!command->SetItem("PlatformID", Input.PlatformID))
		return EErrorCode::DBArgumentError;
	command->SetItem("WID", Input.Wid);
	return EErrorCode::None;
}
