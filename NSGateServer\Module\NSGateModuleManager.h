#pragma once

#include "NSSingleton.h"

class NSClientSession;
class NSGateWorldModule;
class NSGateModule;
class NSGateCharacterModule;
class NSGateLoginModule;
class NSGateMoveServerModule;
class NSGateModuleManager : public TemplateSingleton<NSGateModuleManager>
{
private:
	template<typename TYPE>
	struct GateModuleHolder
	{
		GateModuleHolder()
			: pGateModule(nullptr)
			, LastTimeModuleProcessed(0)
		{
			static_assert(std::is_base_of<NSGateModule, TYPE>(), "TYPE is not a NSGateModule type in GateModuleHolder!");
		}
		GateModuleHolder(TYPE* pNewGateModule)
			: pGateModule(pNewGateModule)
			, LastTimeModuleProcessed(0)
		{
			static_assert(std::is_base_of<NSGateModule, TYPE>(), "TYPE is not a NSGateModule type in GateModuleHolder!");
		}

		void Initialize(TYPE* pNewGateModule);
		void Process(uint64_t nowTick);

		std::unique_ptr<TYPE> pGateModule;
		uint64_t LastTimeModuleProcessed;
	};

public:
	NSGateModuleManager();
	virtual ~NSGateModuleManager();

public:
	void Init();
	auto GetGateLoginModule()->NSGateLoginModule*;
	auto GetGateCharacterModule()->NSGateCharacterModule*;
	auto GetGateModule(EGateModule moduleType)->NSGateModule*;

public:
	void Process();


private:
	GateModuleHolder<NSGateLoginModule> m_GateLoginModule;			//로그인관련 처리를 하는 모듈
	GateModuleHolder<NSGateCharacterModule> m_GateCharacterModule;	//초기 캐릭터 관련 처리를 하는 모듈
	GateModuleHolder<NSGateWorldModule> m_GateWorldModule;	//초기 캐릭터 관련 처리를 하는 모듈
	GateModuleHolder<NSGateMoveServerModule> m_GateMoveServerModule;	//초기 캐릭터 관련 처리를 하는 모듈

	std::unordered_map<int, GateModuleHolder<NSGateWorldModule>> m_MapModuleByModuleId;
};

template<typename TYPE>
void NSGateModuleManager::GateModuleHolder<TYPE>::Initialize(TYPE* pNewGameModule)
{
	pGateModule.reset(pNewGameModule);
	LastTimeModuleProcessed = 0;
}

template<typename TYPE>
void NSGateModuleManager::GateModuleHolder<TYPE>::Process(uint64_t nowTick)
{
	if (nullptr != pGateModule)
	{
		uint64_t elapsedTickCount = 0;
		if (0 < LastTimeModuleProcessed && LastTimeModuleProcessed <= nowTick)
		{
			elapsedTickCount = nowTick - LastTimeModuleProcessed;
		}

		pGateModule->Process(nowTick, elapsedTickCount);
		LastTimeModuleProcessed = nowTick;
	}
}
