#include "stdafx.h"
#include "NSGateCharacterModule.h"

#include "System/NSGateCharacterSystem.h"
#include "System/NSGateChannelSystem.h"

#include "Network/NSClientSession.h"
#include "Network/NSClientSessionManager.h"
#include "Packet/NSPacketData.h"
#include "Network/NSCommunityServerHandler.h"
#include "NPPacket.h"
#include "NPModels.h"
#include "NSModels/NSModels.h"

#include "Log/NSActionLog.h"

NSGateCharacterModule::NSGateCharacterModule()
{
	//시스템
	AddSystem<NSGateCharacterSystem>(this);
	AddSystem<NSGateChannelSystem>(this);
}

void NSGateCharacterModule::Init()
{
	NSGateModule::Init();
}

bool NSGateCharacterModule::OnAddSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateCharacterModule::OnMoveSession(std::shared_ptr<NSClientSession> session)
{
	NSActionLog::LoginWorld(session);
	return true;
}

bool NSGateCharacterModule::OnRemoveSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateCharacterModule::OnDisconnect(std::shared_ptr<NSClientSession>)
{
	return true;
}

void NSGateCharacterModule::HandlePacket(NSPacketData* packetData)
{
	const int64_t socketChannelId = packetData->GetSocketChannelID();
	NPPacket* packet = reinterpret_cast<NPPacket*>(packetData->GetBuffer());

	if (packet == nullptr)
	{
		LOGW << std::format("HandlePacket handles null packet!");
		return;
	}

	std::shared_ptr<NSClientSession> session = GetSessionByChannelId(socketChannelId);
	if (session == nullptr)
		return;

	if (session->IsClosing())
		return;

	// heartbeat 핸들러 없이 바로 처리해준다.
	switch (packet->GetType())
	{
	case EPacketType_1000::ePacketType_HeartbeatReq:
	{
		session->SendHeartbeat();
		return;
	}
	break;
	}

	const uint16_t packetType = packet->GetType();

	if (packetType <= EPacketType_1000::ePacketType_1000_Begin || EPacketType_1000::ePacketType_1000_End <= packetType)
	{
		LOGW << std::format("HandlePacket handles invalid packet type [Type:{}]", packetType);
		return;
	}

	const auto it = m_PacketProcessor.find(packetType);
	if (it != m_PacketProcessor.end())
	{
		it->second(session, packetData->GetBuffer());
		return;
	}

	//캐릭터 선택 / 생성 기능들 게이트에서 처리하도록 변경되어 릴레이 없음
	// kjw : characterModule에서도 전부다 릴레이로 변경
	//session->RelayToWorldServer(packet);
}
