#pragma once

#include "GameServer/NSWorldServerManager.h"
#include "GameServer/NSWorldServer.h"
#include "GameServer/NSWorldPlayer.h"
#include "NPErrorCode.h"
#include "NSModels/NSPacketStruct.h"
#include "Database/NSDBSession.h"

class NSPacket;
class NSWorldServer;
class NPPacketLoginReq;
class NSClientSession : public NSWorldPlayer, public NSDBSession, public std::enable_shared_from_this<NSClientSession>
{
public:
	NSClientSession();
	virtual ~NSClientSession();

public:
	template<typename T>
	bool SendThenClose(T& packet)
	{
		SetSendThenClose(true);
		Send(packet);
		return true;
	}

	template<typename T>
	bool CheckInputTime(T* packet, int intervalMS)
	{
		auto checkSum = GetPayloadCheckSum(reinterpret_cast<char*>(packet) + sizeof(NPPacket), packet->GetSize());
		return CheckInputTime(packet->GetType(), checkSum, intervalMS);
	}

	template<typename T>
	bool Send(T& packet)
	{
		static_assert(!std::is_same_v<T, NPPacket>, "NPPacket is not allowed");

		//패킷인덱스 증가
		if (m_PacketIndex >= 0x3FFF)
			m_PacketIndex = 1;

		packet.SetSequence(++m_PacketIndex);
		if (packet.NeedSerialize())
		{
			char m_SerializeBuffer[g_uMaxPacketSize] = { 0, };
			if (!packet.Serialize(m_SerializeBuffer))
			{
				Close();
				return false;
			}

			return Send(m_SerializeBuffer, packet.GetSize());
		}

		return Send((const char*)&packet, packet.GetSize());
	}

	template<typename T>
	bool WorldServerSend(T* packet)
	{
		static_assert(!std::is_same_v<T, NSPacket>, "NSPacket is not allowed");

		const auto worldServer = NSWorldServerManager::GetInstance()->GetWorldServer(m_WorldServerSid);
		if (worldServer == nullptr)
			return false;

		if (packet->NeedSerialize())
		{
			char m_SerializeBuffer[g_uMaxPacketSize] = { 0, };
			if (!packet->Serialize(m_SerializeBuffer))
			{
				assert(false);
				Close();
				return false;
			}

			worldServer->Send(m_SerializeBuffer, packet->GetSize(), m_WorldServerSessionIndex);
			return true;
		}

		worldServer->Send((char*)packet, packet->GetSize(), m_WorldServerSessionIndex);
		return true;
	}

	template<typename T>
	bool WorldServerNPSend(T* packet)
	{
		static_assert(!std::is_same_v<T, NPPacket>, "NPPacket is not allowed");

		const auto worldServer = NSWorldServerManager::GetInstance()->GetWorldServer(m_WorldServerSid);
		if (worldServer == nullptr)
			return false;

		if (m_PacketIndex >= 0x3FFF)
			m_PacketIndex = 1;

		packet->SetSequence(++m_PacketIndex);
		if (packet->NeedSerialize())
		{
			char m_SerializeBuffer[g_uMaxPacketSize] = { 0, };
			if (!packet->Serialize(m_SerializeBuffer))
			{
				assert(false);
				Close();
				return false;
			}

			worldServer->Send(m_SerializeBuffer, packet->GetSize(), m_WorldServerSessionIndex);
			return true;
		}

		worldServer->Send((char*)packet, packet->GetSize(), m_WorldServerSessionIndex);
		return true;
	}

	bool WorldServerSend(char* packet, int32_t size);

public:
	void Reset();

	void SetPlatformLogin(bool isPlaform);
	bool IsPlatformLogin() const;
	void SetSessionId(const int64 uid);
	auto GetSessionId() const -> int64_t;
	auto GetSocketChannelId() const -> int64_t;
	void SetSocketChannel(const std::shared_ptr<CNLIOInterfaceAdapter>& interfaceRef);
	auto GetSocketChannel() const -> const std::shared_ptr<CNLIOInterfaceAdapter>&;

	bool IsZombie(uint64_t now);

	void Close();
	bool IsClosing() const;
	bool IsClosed() const;
	void Disconnect();

	bool Send(const char* buffer, int size);
	void BypassSend(char* packet);
	void SendSystemNtf(EErrorCode error);
	void SendSystemNtfThenClose(EErrorCode error);
	void SendCommonNtf(EErrorCode error);
	bool SetSendThenClose(bool set);

	void SendHeartbeat();
	auto GetHeartbeatTick() -> uint64_t;

	void SetModuleID(int32_t moduleId);
	auto GetModuleID() -> int32_t;
	void SetAID(int64_t aid);
	auto GetAID() const -> int64_t;
	auto GetCID() const -> int64_t;

	void SetActiveSessionID(const std::string& ActiveSessionID);
	const std::string& GetActiveSessionID() const;

	void SetRedisSessionToken(const std::string& RedisSessionToken);
	const std::string& GetRedisSessionToken() const;

	void SetPlatformAID(const std::string& platformAID);
	void SetCountry(const std::string& country);
	void SetOS(const std::string& OS);
	void SetOSVersion(const std::string& OSVersion);
	void SetMarket(const std::string& market);
	void SetDeviceInfo(const std::string& deviceInfo);
	void SetUILanguage(const std::string& UILanguage);
	void SetPlatformType(const ENPPlatformType platformType);
	void SetPlatformID(const std::string& platformID);
	void SetIdentityProviderType(const ENPPlatformType identityProviderType);
	void SetIdentityProviderID(const std::string& identityProviderID);

	auto GetPlatformAID() const -> const std::string&;
	auto GetCountry() const -> const std::string&;
	auto GetOS() const -> const std::string&;
	auto GetOSVersion() const -> const std::string&;
	auto GetMarket() const -> const std::string&;
	auto GetDeviceInfo() const -> const std::string&;
	auto GetUILanguage() const -> const std::string&;
	auto GetPlatformType() const -> const ENPPlatformType;
	auto GetPlatformID() const -> const std::string&;
	auto GetIdentityProviderType() const -> const ENPPlatformType;
	auto GetIdentityProviderID() const -> const std::string&;

	bool GetPayloadCheckSum(const char* buffer, int size);
	bool CheckInputTime(uint16_t type, uint32_t checkSum, int intervalMS);

	void BypassToWorld(char* buffer, int32_t size);
	bool SetWorldServer(std::shared_ptr<NSWorldServer> pcWorldServer);
	auto GetWorldServerSid() const -> uint64_t;
	void SendCommonNak(EErrorCode error);
	bool IsDummyUser() const;
	void SetDummyUserInfo(NPDummyClientInfo dummyUserInfo);
	auto GetWorldServerSessionIndex() const -> int64_t;

	const std::string& GetIpAddress();
	void GetActionLogHeader(NSKGLogger* outLogger, const std::string& category, const std::string& label, const std::string& action, const int32_t transactionKey) const;

private:
	int64_t m_SessionId = -1;
	int64_t m_AID{ 0 };
	int64_t m_CID{ 0 };
	std::string m_ActiveSessionID = "";
	std::string m_RedisSessionToken = "";

	std::string m_PlatformAID = ""; // to be deprecated
	std::string m_Country = "";
	std::string m_OS = "";
	std::string m_OSVersion = "";
	std::string m_Market = "";
	std::string m_DeviceInfo = "";
	std::string m_UILanguage = "";

	// DB에 저장될 내용 기준으로 정의
	ENPPlatformType m_PlatformType{};
	std::string m_PlatformID;
	ENPPlatformType m_IdentityProviderType{};
	std::string m_IdentityProviderID;

	int64_t m_SocketChannelId = 0;
	int32_t m_ModuleID = 0;

	uint64_t m_WorldServerSid = 0;
	int64_t m_WorldServerSessionIndex = 0;

	uint64_t m_HeartbeatTick = 0;
	uint16_t m_PacketSequence = 0;
	uint16_t m_PacketIndex{ 0 };

	bool m_Closing = false;

	std::shared_ptr<CNLIOInterfaceAdapter> m_SocketChannel = nullptr;

	//송신 후 종료 플래그를 소켓 객체에 직접 세트시
	//post된 send가 있을경우 정상적으로 동작하지 않는 문제가 생겨
	//재구현 되었습니다.
	//플래그 세트는 session에서 이루어집니다.  
	bool m_SendThenClose = false;

	std::map<uint16_t, std::pair<uint32_t, uint64_t>> m_InputTimes = {};
	NPDummyClientInfo m_DummyUserInfo = {};
	bool m_IsPlatformLogin = false;
	uint64_t m_CreateSessionTick = 0;
};
