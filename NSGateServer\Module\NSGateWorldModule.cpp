#include "stdafx.h"
#include "NSGateWorldModule.h"

#include "Network/NSClientSession.h"
#include "Network/NSClientSessionManager.h"
#include "Network/NSCommunityServerHandler.h"

#include "Data/NSCharacterTemplate.h"
#include "Data/NSWorldTriggerTemplate.h"
#include "Data/NSSummonTemplate.h"
#include "Data/NSCampTemplate.h"
#include "Data/NSGameConfigTemplate.h"
#include "Packet/NSPacketData.h"
#include "NPModels.h"
#include "NSRandom.h"
#include "NSModels/NSModels.h"

NSGateWorldModule::NSGateWorldModule()
{

}

NSGateWorldModule::~NSGateWorldModule()
{
	NSGateWorldModule::Reset();
}

void NSGateWorldModule::Init()
{
	NSGateModule::Init();
}

void NSGateWorldModule::Reset()
{
	NSGateModule::Reset();
}

void NSGateWorldModule::Process(uint64_t nowTick, uint64_t elapsedTickCount)
{
	NSGateModule::Process(nowTick, elapsedTickCount);
}

bool NSGateWorldModule::OnAddSession(std::shared_ptr<NSClientSession> session)
{
	// 여기는 MoveSession으로만 올 수 있다
	LOGE << std::format("[Server] WorldModule - Invalid AddSession[SocketChannelId:{}]", session->GetSessionId());
	return false;
}

bool NSGateWorldModule::OnMoveSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateWorldModule::OnRemoveSession(std::shared_ptr<NSClientSession>)
{
	return true;
}

bool NSGateWorldModule::OnDisconnect(std::shared_ptr<NSClientSession>)
{
	return true;
}

void NSGateWorldModule::HandlePacket(NSPacketData* packetData)
{
	const int64_t socketChannelId = packetData->GetSocketChannelID();
	NPPacket* packet = reinterpret_cast<NPPacket*>(packetData->GetBuffer());

	if (packet == nullptr)
	{
		LOGW << std::format("HandlePacket handles null packet!");
		return;
	}

	std::shared_ptr<NSClientSession> session = GetSessionByChannelId(socketChannelId);
	if (session == nullptr)
		return;

	if (session->IsClosing())
		return;

	const std::string packetName = NPPacketType_1000_GetPacketName(packet->GetType());
	const uint16_t packetType = packet->GetType();

	if (packetType <= EPacketType_1000::ePacketType_1000_Begin || EPacketType_1000::ePacketType_1000_End <= packetType)
	{
		LOGW << std::format("Recv Invalid Packet SessionId: {}, Name: {}, Code: {}, Size: {}/Byte",
			session->GetSessionId(), packetName, packetType, packet->GetSize());
		return;
	}

	if (packetType != EPacketType_1000::ePacketType_HeartbeatReq &&
		packetType != EPacketType_1000::ePacketType_HeartbeatAck &&
		packetType != EPacketType_1000::ePacketType_MoveReq)
	{
		LOGD << std::format("Recv WorldModule Packet SessionId: {}, Name: {}, Code: {}, Size: {}/Byte",
			session->GetSessionId(), packetName, packetType, packet->GetSize());
	}

	if (packet->ShouldSendCommunityServer()
		&& packet->GetType() != EPacketType_1000::ePacketType_PartyDungeonVoteReq
		&& packet->GetType() != EPacketType_1000::ePacketType_PartyDungeonVoteCancelReq)
	{
		const std::shared_ptr<NSCommunityServerSession> communitySession = NSCommunityServerHandler::GetInstance()->GetSession(socketChannelId);
		if (communitySession != nullptr)
		{
			packet->SetGateClientSessionId(session->GetSessionId());
			communitySession->Send(packetData->GetBuffer(), packetData->GetSize());
		}
		return;
	}
	if (packetType == EPacketType_1000::ePacketType_XignCodeDataReq)
	{
		const auto it = m_PacketProcessor.find(packetType);
		if (it != m_PacketProcessor.end())
		{
			it->second(session, packetData->GetBuffer());
			return;
		}
	}

	// kjw : Map Module일때는 Relay
	packet->SetGateClientSessionId(session->GetSessionId());
	session->BypassToWorld((char*)packet, packet->GetSize());
}

