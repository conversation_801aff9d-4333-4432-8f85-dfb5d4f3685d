#pragma once

#include "DataBase/StoredProcedure/NSStoredProcedure.h"

class SpLoginAccount : public NSStoredProcedure
{
	DECLARE_PROCEDURE_UTIL;

protected:
	static constexpr const char* procedureName = "spLoginAccount";
	static constexpr EDataBase procedureHost = EDataBase::Game;
	EErrorCode MakeQuery(NSAdoCommand* command);

public:
	struct Input
	{
		int64_t Aid = 0;
		int32_t Wid = 0;
		char PlatformAccountID[g_uMaxPlatformIDLength] = { 0, };

		ENPPlatformType PlatformType{};
		char PlatformID[g_uMaxPlatformIDLength]{ 0 };
		ENPPlatformType IdentityProviderType{};
		char IdentityProviderID[g_uMaxPlatformIDLength]{ 0 };
	} Input;

	SpLoginAccount() = default;
	SpLoginAccount(const int64_t aid, const int32_t wid, const std::string_view PlatformAccountID,
		ENPPlatformType platformType, const std::string_view platformId, ENPPlatformType identityProviderType, const std::string_view identityProviderID);
};
